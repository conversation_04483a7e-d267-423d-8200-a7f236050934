{"__bytecode.const": {"blob_name": ".bytecode", "blob_size": 4271245, "input_size": 4295165}, "__constants.const": {"blob_name": "", "blob_size": 819, "input_size": 2108}, "module.__main__.const": {"blob_name": "__main__", "blob_size": 47366, "input_size": 68046}, "module.__parents_main__.const": {"blob_name": "__parents_main__", "blob_size": 47433, "input_size": 68235}, "module.multiprocessing-postLoad.const": {"blob_name": "multiprocessing-postLoad", "blob_size": 364, "input_size": 692}, "module.multiprocessing-preLoad.const": {"blob_name": "multiprocessing-preLoad", "blob_size": 226, "input_size": 471}, "module.tkinter-preLoad.const": {"blob_name": "tkinter-preLoad", "blob_size": 187, "input_size": 406}, "total": 4216}