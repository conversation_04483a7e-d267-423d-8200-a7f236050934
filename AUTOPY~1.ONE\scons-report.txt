CC=<clcache>
CCCOM=${TEMPFILE("$CC $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $CFLAGS $CCFLAGS $_CCCOMCOM","$CCCOMSTR")}
CFILESUFFIX=.c
CLCACHE_DIR=C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\clcache
CLCACHE_STATS=D:\jiedan\8E91D~1.1修\AUTOPY~1.ONE\clcache-stats.29572.txt
CPPDEFINES=['_NUITKA_STANDALONE_MODE', '_NUITKA_ONEFILE_MODE', '_NUITKA_ONEFILE_TEMP_BOOL', '_NUITKA_DLL_MODE', '_NUITKA_ONEFILE_DLL_MODE', '_WIN32_WINNT=0x0601', '__NUITKA_NO_ASSERT__', '_NUITKA_EXE_MODE', '_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1']
CPPDEFPREFIX=/D
CPPDEFSUFFIX=
CPPPATH=['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib', '.', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\include', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\static_src', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd']
CPPSUFFIXES=['.c', '.C', '.cxx', '.cpp', '.c++', '.cc', '.h', '.H', '.hxx', '.hpp', '.hh', '.F', '.fpp', '.FPP', '.m', '.mm', '.S', '.spp', '.SPP', '.sx']
CXX=<clcache>
CXXCOM=${TEMPFILE("$CXX $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $CXXFLAGS $CCFLAGS $_CCCOMCOM","$CXXCOMSTR")}
CXXFILESUFFIX=.cc
HOST_ARCH=x86_64
HOST_OS=win32
INCPREFIX=/I
INCSUFFIX=
LDMODULE=$SHLINK
LDMODULEFLAGS=$SHLINKFLAGS
LDMODULEPREFIX=$SHLIBPREFIX
LDMODULESUFFIX=$SHLIBSUFFIX
LIBDIRPREFIX=/LIBPATH:
LIBDIRSUFFIX=
LIBLINKPREFIX=
LIBLINKSUFFIX=$LIBSUFFIX
LIBPATH=[]
LIBPREFIX=
LIBPREFIXES=['$LIBPREFIX']
LIBS=['Shell32', 'PsApi']
LIBSUFFIX=.lib
LIBSUFFIXES=['$LIBSUFFIX']
LINK=link
MSVSBUILDCOM=$MSVSSCONSCOM "$MSVSBUILDTARGET"
MSVSCLEANCOM=$MSVSSCONSCOM -c "$MSVSBUILDTARGET"
MSVSENCODING=utf-8
MSVSPROJECTSUFFIX=${GET_MSVSPROJECTSUFFIX}
MSVSREBUILDCOM=$MSVSSCONSCOM "$MSVSBUILDTARGET"
MSVSSCONSCOM=$MSVSSCONS $MSVSSCONSFLAGS
MSVSSCONSFLAGS=-C "${MSVSSCONSCRIPT.dir.get_abspath()}" -f ${MSVSSCONSCRIPT.name}
MSVSSOLUTIONSUFFIX=${GET_MSVSSOLUTIONSUFFIX}
MSVS_VERSION=14.3
MT=mt
MTEXECOM=-$MT $MTFLAGS -manifest ${TARGET}.manifest $_MANIFEST_SOURCES -outputresource:$TARGET;1
MTSHLIBCOM=-$MT $MTFLAGS -manifest ${TARGET}.manifest $_MANIFEST_SOURCES -outputresource:$TARGET;2
OBJPREFIX=
OBJSUFFIX=.obj
PCHCOM=$CXX /Fo${TARGETS[1]} $CXXFLAGS $CCFLAGS $CPPFLAGS $_CPPDEFFLAGS $_CPPINCFLAGS /c $SOURCES /Yc$PCHSTOP /Fp${TARGETS[0]} $CCPDBFLAGS $PCHPDBFLAGS
PCHPDBFLAGS=
PLATFORM=win32
PROGPREFIX=
PROGSUFFIX=.exe
RC=rc
RCCOM=$RC $_CPPDEFFLAGS $_CPPINCFLAGS $RCFLAGS /fo$TARGET $SOURCES
RCSUFFIXES=['.rc', '.rc2']
REGSVR=C:\Windows\System32\regsvr32
REGSVRCOM=$REGSVR $REGSVRFLAGS ${TARGET.windows}
REGSVRFLAGS=/s 
SHCC=$CC
SHCCCOM=${TEMPFILE("$SHCC $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $SHCFLAGS $SHCCFLAGS $_CCCOMCOM","$SHCCCOMSTR")}
SHCXX=$CXX
SHCXXCOM=${TEMPFILE("$SHCXX $_MSVC_OUTPUT_FLAG /c $CHANGED_SOURCES $SHCXXFLAGS $SHCCFLAGS $_CCCOMCOM","$SHCXXCOMSTR")}
SHELL=C:\Windows\System32\cmd.exe
SHLIBPREFIX=
SHLIBSUFFIX=.dll
SHLINK=$LINK
SHOBJPREFIX=$OBJPREFIX
SHOBJSUFFIX=$OBJSUFFIX
TARGET_ARCH=x86_64
TEMPFILEARGJOIN=

TEMPFILEPREFIX=@
TOOLS=['default', 'mslink', 'msvc', 'msvs']
VSWHERE=C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe
WINDOWSDEFPREFIX=
WINDOWSDEFSUFFIX=.def
WINDOWSEXPPREFIX=
WINDOWSEXPSUFFIX=.exp
WINDOWSPROGMANIFESTPREFIX=
WINDOWSPROGMANIFESTSUFFIX=${PROGSUFFIX}.manifest
WINDOWSSHLIBMANIFESTPREFIX=
WINDOWSSHLIBMANIFESTSUFFIX=${SHLIBSUFFIX}.manifest
WindowsSDKVersion=10.0.26100.0
gcc_mode=False
clang_mode=False
msvc_mode=True
mingw_mode=False
clangcl_mode=False
PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\VCPackages;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\bin\Roslyn;C:\Program Files\Microsoft Visual Studio\2022\Community\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files\Microsoft Visual Studio\2022\Community\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\;C:\Windows\System32;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\vcpkg;D:\ImageMagick-7.1.2-Q16-HDRI;c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;C:\ProgramData\Oracle\Java\javapath;C:\Windows;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\;D:\WinSCP\;D:\xshell\;D:\nodejs\;D:\cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\MinGW\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\maven-3.6.3\bin;C:\Program Files\cursor\resources\app\bin;%pyenv%\bin;%pyenv%\shims;C:\Tools\;D:\IntelliJ IDEA 2019.2.4\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;D:\pyenv\pyenv-win\bin;D:\pyenv\pyenv-win\shims
TARGET=D:\jiedan\8E91D~1.1修\auto.pyc_Decompiled.exe
