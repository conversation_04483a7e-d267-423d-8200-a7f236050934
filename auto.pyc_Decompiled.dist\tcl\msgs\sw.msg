# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset sw DAYS_OF_WEEK_ABBREV [list \
        "Jpi"\
        "Jtt"\
        "Jnn"\
        "Jtn"\
        "Alh"\
        "<PERSON><PERSON>"\
        "J<PERSON>"]
    ::msgcat::mcset sw DAYS_OF_WEEK_FULL [list \
        "Juma<PERSON><PERSON>"\
        "Jumatatu"\
        "Jumanne"\
        "Jumatano"\
        "<PERSON>ham<PERSON>"\
        "<PERSON>jumaa"\
        "Jumamosi"]
    ::msgcat::mcset sw MONTHS_ABBREV [list \
        "Jan"\
        "Feb"\
        "Mar"\
        "Apr"\
        "Mei"\
        "Jun"\
        "Jul"\
        "Ago"\
        "Sep"\
        "Okt"\
        "Nov"\
        "Des"\
        ""]
    ::msgcat::mcset sw MONTHS_FULL [list \
        "<PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "April<PERSON>"\
        "<PERSON>"\
        "<PERSON><PERSON>"\
        "<PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "<PERSON>em<PERSON>"\
        "Oktoba"\
        "<PERSON>em<PERSON>"\
        "Desemba"\
        ""]
    ::msgcat::mcset sw BCE "KK"
    ::msgcat::mcset sw CE "BK"
}
