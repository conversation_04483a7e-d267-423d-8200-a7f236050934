# -*- coding: utf-8 -*-
# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: auto.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

try:
    import ctypes
    import sys
    hwnd = ctypes.windll.kernel32.GetConsoleWindow()
    if hwnd != 0:
        ctypes.windll.user32.ShowWindow(hwnd, 0)
except Exception as e:
    print(f'隐藏控制台窗口失败: {e}')
import os
import sys
import random
import math
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser, font, simpledialog
import threading
import subprocess
import time
import tempfile
import platform
import traceback
import shutil
import logging
import concurrent.futures
import datetime
import glob
import json
import re
import ctypes
import gc
import multiprocessing
import uuid

class VideoProcessingThread(threading.Thread):
    def __init__(self, target_file, subfolders, options, update_progress, completion_callback):
        super(VideoProcessingThread, self).__init__()
        self.target_file = target_file
        self.subfolders = subfolders
        self.options = options
        self.update_progress = update_progress
        self.completion_callback = completion_callback
        self.is_canceled = False
        self.temp_dirs = []
        output_dir = self.options.get('output_dir')
        self.temp_dir = tempfile.mkdtemp(prefix='文运躺音_', dir=output_dir if output_dir else None)
        self.temp_dirs.append(self.temp_dir)
        self.logger = Logger()
        self.startupinfo = None
        self.creation_flags = 0
        if platform.system() == 'Windows':
            self.startupinfo = subprocess.STARTUPINFO()
            self.startupinfo.dwFlags = subprocess.STARTF_USESHOWWINDOW
            self.startupinfo.wShowWindow = subprocess.SW_HIDE
            self.creation_flags = subprocess.CREATE_NO_WINDOW

    def _get_audio_files(self, subfolder):
        """获取子文件夹中的音频文件列表"""  # inserted
        audio_folder = os.path.join(subfolder, '音频')
        if os.path.exists(audio_folder):
            return [f for f in os.listdir(audio_folder) if f.lower().endswith(('.mp3', '.wav', '.m4a', '.aac'))]
        return []

    def _natural_sort_key(self, s):
        """自然排序的键函数，使数字序列按照数值大小排序"""  # inserted

        def convert(text):
            return int(text) if text.isdigit() else text.lower()
        import re
        return [convert(c) for c in re.split('(\\d+)', s)]

    def run(self):
        """运行视频处理线程"""  # inserted
        self.logger.info(f'开始处理文件夹: {self.target_file}')
        start_time = time.time()
        processed_subfolder_count = 0
        processed_audio_count = 0
        self.failed_audio_count_local = 0
        all_merged_original_videos = []
        try:
            self.update_progress(0, '初始化处理环境...')
            total_subfolders = len(self.subfolders)
            total_audio_files = sum([len(self._get_audio_files(subfolder)) for subfolder in self.subfolders])
            self.logger.info(f'总子文件夹数: {total_subfolders}, 总音频文件数: {total_audio_files}')
            max_progress = 90
            progress_per_subfolder = max_progress / total_subfolders if total_subfolders > 0 else 0
            current_progress = 0
            self.update_progress(current_progress, f'开始处理... 剩余文件夹: {total_subfolders}, 剩余音频: {total_audio_files}')
            if self.options.get('parallel_processing', False):
                requested_workers = self.options.get('max_workers', 4)
                max_workers = min(4, requested_workers)
                self.logger.info(f'启用并行处理，最大线程数: {max_workers} (CPU核心数: {os.cpu_count()})')
            else:
                max_workers = 1
                self.logger.info('使用单线程顺序处理')
            self.max_workers = max_workers
            param_info = [f'处理目标: {self.target_file}', f'有效子文件夹: {len(self.subfolders)}', f"并行处理: {('是' if max_workers > 1 else '否')}, 最大线程数: {max_workers}", f"硬件加速: {('是' if self.options.get('hardware_acceleration', False) else '否')}"]
            if self.options.get('add_bgm', False):
                param_info.append('添加BGM: 是')
                bgm_path = self.options.get('bgm_path', '')
                if bgm_path:
                    param_info.append(f'BGM路径: {os.path.basename(bgm_path)}')
                param_info.append(f"BGM音量: {int(self.options.get('bgm_volume', 0.2) * 100)}%")
            if self.options.get('add_subtitles', False):
                param_info.append('添加字幕: 是')
                subtitle_style = self.options.get('subtitle_style', {})
                if subtitle_style:
                    param_info.append(f"字幕样式: {subtitle_style.get('font', '默认字体')}, 大小:{subtitle_style.get('font_size', 16)}")
            if self.options.get('create_merged_video', False):
                param_info.append('创建合并视频: 是')
            for param in param_info:
                self.logger.info(param)
            audio_progress_total = 80
            merge_progress_total = 20
            audio_progress_per_file = audio_progress_total / total_audio_files if total_audio_files > 0 else 0
            merge_progress_per_folder = merge_progress_total / total_subfolders if total_subfolders > 0 and self.options.get('create_merged_video', False) else 0
            global_tasks = []
            processed_videos_by_subfolder = {}
            subfolder_info = {}
            for subfolder_index, subfolder in enumerate(self.subfolders):
                if self.is_canceled:
                    break
                audio_folder = os.path.join(subfolder, '音频')
                video_folder = os.path.join(subfolder, '视频')
                if not os.path.exists(audio_folder) or not os.path.exists(video_folder):
                    self.logger.warning(f'子文件夹 {subfolder} 不存在音频或视频文件夹，跳过')
                    continue
                output_dir = os.path.join(subfolder, '处理完成')
                os.makedirs(output_dir, exist_ok=True)
                audio_files = [f for f in os.listdir(audio_folder) if f.endswith('.mp3')]
                video_files = [f for f in os.listdir(video_folder) if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
                if not audio_files or not video_files:
                    self.logger.warning(f'子文件夹 {subfolder} 缺少音频或视频文件，跳过')
                    continue
                audio_files = sorted(audio_files, key=self._natural_sort_key)
                subfolder_info[subfolder] = {'audio_folder': audio_folder, 'video_folder': video_folder, 'output_dir': output_dir, 'video_files': video_files}
                processed_videos_by_subfolder[subfolder] = []
                for audio_index, audio_file in enumerate(audio_files):
                    global_tasks.append((subfolder, audio_file, audio_index, subfolder_index))
            self.logger.info(f'已创建全局任务队列，共 {len(global_tasks)} 个任务')
            if self.options.get('parallel_processing', True) and max_workers > 1:
                self.logger.info('使用并行处理模式处理全局任务队列')
                futures_dict = {}
                pending_tasks = global_tasks.copy()
                with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    active_futures = set()
                    while pending_tasks or active_futures:
                        while len(active_futures) < self.max_workers and pending_tasks:
                            task = pending_tasks.pop(0)
                            subfolder, audio_file, audio_index, subfolder_index = task
                            if self.is_canceled:
                                break
                            info = subfolder_info[subfolder]
                            self.logger.info(f'提交任务: 子文件夹 {os.path.basename(subfolder)} 的音频 {audio_file} (总剩余任务: {len(pending_tasks)})')
                            future = executor.submit(self.process_audio_file_with_isolation, subfolder, info['audio_folder'], info['video_folder'], info['output_dir'], audio_file, audio_index, processed_audio_count, total_audio_files, info['video_files'])
                            futures_dict[future] = (subfolder, audio_file)
                            active_futures.add(future)
                        if active_futures:
                            done, not_done = concurrent.futures.wait(active_futures, timeout=0.5, return_when=concurrent.futures.FIRST_COMPLETED)
                            for future in done:
                                subfolder, audio_file = futures_dict[future]
                                try:
                                    result = future.result()
                                    if result:
                                        processed_videos_by_subfolder[subfolder].append(result)
                                        processed_audio_count += 1
                                        current_progress = processed_audio_count * audio_progress_per_file
                                        remaining_folders = total_subfolders - processed_subfolder_count
                                        remaining_audio_files = total_audio_files - processed_audio_count
                                        self.update_progress(current_progress, f'处理音频 [{processed_audio_count}/{total_audio_files}], 剩余文件夹: {remaining_folders}, 剩余音频: {remaining_audio_files}')
                                        self.logger.info(f'任务完成: {os.path.basename(subfolder)}/{audio_file} - 即将启动新任务...')
                                except Exception as e:
                                    self.logger.error(f'处理音频文件 {os.path.basename(subfolder)}/{audio_file} 时出错: {str(e)}')
                                    traceback.print_exc()
                                active_futures.remove(future)
                                del futures_dict[future]
                        if self.is_canceled:
                            self.logger.info('处理被取消，终止所有任务')
                            break
                    for future in active_futures:
                        future.cancel()
            else:
                self.logger.info('使用单线程模式处理全局任务队列')
                for task in global_tasks:
                    if self.is_canceled:
                        break
                    subfolder, audio_file, audio_index, subfolder_index = task
                    info = subfolder_info[subfolder]
                    self.logger.info(f'处理: 子文件夹 {os.path.basename(subfolder)} 的音频 {audio_file}')
                    result = self.process_audio_file(subfolder, info['audio_folder'], info['video_folder'], info['output_dir'], audio_file, audio_index, processed_audio_count, total_audio_files, info['video_files'])
                    if result:
                        processed_videos_by_subfolder[subfolder].append(result)
                        processed_audio_count = processed_audio_count + 1
                        current_progress = current_progress + audio_progress_per_file
                        remaining_folders = total_subfolders - processed_subfolder_count
                        remaining_audio_files = total_audio_files - processed_audio_count
                        self.update_progress(current_progress, f'处理音频 [{processed_audio_count}/{total_audio_files}], 剩余文件夹: {remaining_folders}, 剩余音频: {remaining_audio_files}')
            self.logger.info('音频处理完成，继续处理子文件夹合并和原视频合并任务')
            processed_subfolder_count = 0
            for subfolder_index, subfolder in enumerate(self.subfolders):
                if self.is_canceled:
                    break
                processed_videos = processed_videos_by_subfolder.get(subfolder, [])
                output_dir = os.path.join(subfolder, '处理完成')
                os.makedirs(output_dir, exist_ok=True)
                if not processed_videos:
                    self.logger.warning(f'子文件夹 {os.path.basename(subfolder)} 没有成功处理的视频')
                else:
                    self.logger.info(f'子文件夹 {os.path.basename(subfolder)} 有 {len(processed_videos)} 个成功处理的视频')
                if processed_videos and self.options.get('create_merged_video', False):
                    merged_video_path = os.path.join(output_dir, '合并视频.mp4')
                    self.logger.info(f'合并子文件夹 {os.path.basename(subfolder)} 的视频到: {merged_video_path}')
                    self.update_progress(current_progress, f'合并子文件夹视频 [{processed_subfolder_count + 1}/{total_subfolders}]')
                    self.merge_videos(processed_videos, merged_video_path)
                    current_progress = current_progress + merge_progress_per_folder
                    self.update_progress(current_progress, f'完成子文件夹 [{processed_subfolder_count + 1}/{total_subfolders}]')
                if self.options.get('merge_original_video', False):
                    self.logger.info(f'尝试合并子文件夹 {os.path.basename(subfolder)} 的原视频与1号处理视频')
                    merged_original_path = self.merge_original_with_first_video(subfolder, output_dir)
                    if merged_original_path:
                        self.logger.info(f'成功合并原视频与1号处理视频: {merged_original_path}')
                        all_merged_original_videos.append(merged_original_path)
                    else:
                        self.logger.warning(f'子文件夹 {os.path.basename(subfolder)} 原视频合并失败或跳过')
                processed_subfolder_count = processed_subfolder_count + 1
            if len(all_merged_original_videos) > 1 and self.options.get('create_merged_video', False) and self.options.get('merge_original_video', False):
                self.logger.info('开始合并所有原视频与1号视频的结果...')
                self.update_progress(96, '合并所有原视频与1号视频的结果')
                final_output_dir = os.path.join(self.target_file, '最终输出')
                os.makedirs(final_output_dir, exist_ok=True)
                final_merged_path = os.path.join(final_output_dir, '所有原视频与1号视频合并.mp4')
                self.merge_videos(all_merged_original_videos, final_merged_path)
                self.logger.info(f'所有原视频与1号视频合并成功: {final_merged_path}')
            total_time = time.time() - start_time
            minutes, seconds = divmod(total_time, 60)
            hours, minutes = divmod(minutes, 60)
            time_str = f'{int(hours)}小时{int(minutes)}分{int(seconds)}秒' if hours > 0 else f'{int(minutes)}分{int(seconds)}秒'
            success_message = f'处理完成! 共处理{processed_subfolder_count}个文件夹, {processed_audio_count}个音频文件, 总耗时: {time_str}'
            self.logger.info(success_message)
            self.update_progress(100, f'处理完成! 共处理{processed_subfolder_count}个文件夹, {processed_audio_count}个音频文件')
            if self.options.get('clean_temp_files', True):
                try:
                    if self.temp_dirs and all((os.path.exists(temp_dir) for temp_dir in self.temp_dirs)):
                        for temp_dir in self.temp_dirs:
                            shutil.rmtree(temp_dir)
                            self.logger.info(f'清理临时目录: {temp_dir}')
                except Exception as e:
                    self.logger.warning(f'清理临时目录失败: {self.temp_dirs}, 错误: {str(e)}')
            else:
                self.logger.info(f'保留临时目录: {self.temp_dirs}')
            if self.completion_callback:
                self.completion_callback(True, success_message)
            end_time = time.time()
            total_time = end_time - start_time
            hours, remainder = divmod(total_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            failed_audio_count = getattr(self, 'failed_audio_count_local', 0)
            result_summary = f'处理完成! 共处理{processed_subfolder_count}个文件夹, {processed_audio_count}个音频文件'
            if failed_audio_count > 0:
                result_summary = result_summary + f', {failed_audio_count}个文件失败'
            result_summary = result_summary + f', 总耗时: {int(hours)}时{int(minutes)}分{int(seconds)}秒'
            self.logger.info(result_summary)
            disk_space_error = False
            for temp_dir in self.temp_dirs:
                if os.path.exists(temp_dir):
                    free_space = self._get_free_disk_space(temp_dir)
                    if free_space < 1073741824:
                        disk_space_error = True
                        break
            if disk_space_error:
                self.logger.error('处理失败: 磁盘空间严重不足，请清理磁盘空间后重试')
                if self.completion_callback:
                    self.completion_callback(False, '处理失败: 磁盘空间不足，请清理磁盘空间后重试')
                try:
                    time.sleep(1)
                    self._clean_temp_dirs()
                except Exception as e:
                    self.logger.warning(f'最终清理临时目录时出错: {str(e)}')
                return
            success = failed_audio_count == 0
            message = result_summary
            if not success:
                message = f'部分文件处理失败。{result_summary}，请检查日志获取详细信息。'
            self._clean_temp_dirs()
            if self.completion_callback:
                self.completion_callback(success, message)
        except Exception as e:
            self.logger.error(f'处理过程中发生错误: {str(e)}')
            traceback.print_exc()
            if self.completion_callback:
                end_time = time.time()
                total_time = end_time - start_time
                hours, remainder = divmod(total_time, 3600)
                minutes, seconds = divmod(remainder, 60)
                error_message = f'处理失败: {str(e)}，总耗时: {int(hours)}时{int(minutes)}分{int(seconds)}秒'
                self.completion_callback(False, error_message)
            self._clean_temp_dirs()
        finally:
            try:
                time.sleep(1)
                self._clean_temp_dirs()
            except Exception as e:
                self.logger.warning(f'最终清理临时目录时出错: {str(e)}')

    def process_audio_file_with_isolation(self, subfolder, audio_folder, video_folder, output_dir, audio_file, audio_index, processed_audio_count, total_audio_files, video_files):
        """\n        在一个隔离的环境中处理单个音频文件，包括创建和清理独立的临时目录。\n        """  # inserted
        audio_name_for_dir = os.path.splitext(os.path.basename(audio_file))[0]
        task_id = f'{audio_name_for_dir}_{uuid.uuid4().hex[:8]}'
        task_temp_dir = os.path.join(self.temp_dir, f'task_{task_id}')
        os.makedirs(task_temp_dir, exist_ok=True)
        try:
            return self.process_audio_file(subfolder, audio_folder, video_folder, output_dir, audio_file, audio_index, processed_audio_count, total_audio_files, video_files, thread_temp_dir=task_temp_dir)
        finally:  # inserted
            try:
                if os.path.exists(task_temp_dir):
                    shutil.rmtree(task_temp_dir)
                    self.logger.debug(f'清理了任务临时目录: {task_temp_dir}')
            except Exception as e:
                self.logger.error(f'清理任务临时目录 {task_temp_dir} 失败: {e}')

    def process_audio_file(self, subfolder, audio_folder, video_folder, output_dir, audio_file, audio_index, processed_audio_count, total_audio_files, video_files, thread_temp_dir=None):
        """核心处理函数：为单个音频文件匹配视频、添加BGM和字幕"""  # inserted
        subfolder_temp_dir = tempfile.mkdtemp(prefix='临时_', dir=output_dir)
        self.temp_dirs.append(subfolder_temp_dir)
        audio_path = os.path.join(audio_folder, audio_file)
        audio_base_name = os.path.splitext(audio_file)[0]
        self.logger.info(f'开始处理音频文件: {audio_file} (输出基础名: {audio_base_name})')
        try:
            process_start_time = time.time()
            video_path = None
            if len(video_files) > 0:
                video_index = audio_index % len(video_files)
                random_video = video_files[video_index]
                video_path = os.path.join(video_folder, random_video)
                self.logger.info(f'为音频{audio_index}分配视频{video_index}: {random_video}')
            else:
                self.logger.error('没有可用的视频文件')
                return
            subtitle_path = None
            potential_subtitle_path = os.path.join(audio_folder, f'{audio_base_name}.srt')
            if os.path.exists(potential_subtitle_path):
                subtitle_path = potential_subtitle_path
                self.logger.info(f'找到音频同名字幕文件: {subtitle_path}')
            else:  # inserted
                subtitle_folder = os.path.join(subfolder, '字幕')
                if os.path.exists(subtitle_folder):
                    potential_subtitle_path = os.path.join(subtitle_folder, f'{audio_base_name}.srt')
                    if os.path.exists(potential_subtitle_path):
                        subtitle_path = potential_subtitle_path
                        self.logger.info(f'找到字幕文件夹中的字幕文件: {subtitle_path}')
            if subtitle_path is None and self.options.get('add_subtitles', False):
                self.logger.warning(f'未找到对应的字幕文件: {audio_base_name}.srt')
            subfolder_temp_dir = thread_temp_dir if thread_temp_dir else os.path.join(output_dir, f'临时_{audio_base_name}_{int(time.time())}')
            os.makedirs(subfolder_temp_dir, exist_ok=True)
            self.temp_dirs.append(subfolder_temp_dir)
            output_video_path = os.path.join(output_dir, f'{audio_base_name}.mp4')
            temp_output = os.path.join(subfolder_temp_dir, f'temp_{audio_base_name}.mp4')
            self.logger.info(f'开始一步合成: 视频 {os.path.basename(video_path)} + 音频 {audio_file}')
            try:
                audio_info = self._get_video_info(audio_path)
                if audio_info and 'duration' in audio_info:
                    audio_duration = float(audio_info.get('duration', 0))
                    self.logger.info(f'音频时长: {audio_duration}秒')
                    if audio_duration > 600:
                        self.logger.warning(f'警告：音频时长较长 ({audio_duration:.2f}秒)，处理可能需要较长时间')
            except Exception as e:
                self.logger.warning(f'获取音频时长时出错: {str(e)}')
            processed_output = os.path.join(subfolder_temp_dir, f'temp_{audio_index + 1}.mp4')
            success = self.process_video_with_audio(video_path, audio_path, processed_output)
            if not success:
                self.logger.error(f'一步合成失败: {processed_output}')
                return
            self.logger.info(f'一步合成成功: {processed_output}')
            current_file = processed_output
            if self.options.get('add_bgm', False) and 'bgm_path' in self.options and os.path.exists(self.options['bgm_path']):
                bgm_output = os.path.join(subfolder_temp_dir, f'with_bgm_{audio_base_name}.mp4')
                self.logger.info(f'添加背景音乐到视频: {os.path.basename(current_file)}')
                self.update_progress(60, '添加背景音乐...')
                bgm_volume = self.options.get('bgm_volume', 0.2)
                self.logger.info(f'使用BGM音量: {bgm_volume}')
                bgm_success = self._add_bgm_to_video(current_file, bgm_output, self.options['bgm_path'], bgm_volume)
                if bgm_success:
                    self.logger.info(f'背景音乐添加成功: {os.path.basename(bgm_output)}')
                    current_file = bgm_output
                else:  # inserted
                    self.logger.error('背景音乐添加失败，将使用无BGM版本继续')
            else:  # inserted
                if not self.options.get('add_bgm', False):
                    self.logger.info('未勾选添加BGM选项，跳过BGM添加')
                else:  # inserted
                    if 'bgm_path' not in self.options:
                        self.logger.warning('未设置BGM路径，跳过BGM添加')
                    else:  # inserted
                        if not os.path.exists(self.options.get('bgm_path', '')):
                            self.logger.warning(f"BGM文件不存在: {self.options.get('bgm_path', '')}, 跳过BGM添加")
            if self.options.get('add_subtitles', False) and subtitle_path:
                subtitle_output = os.path.join(subfolder_temp_dir, f'with_subtitle_{audio_base_name}.mp4')
                self.logger.info(f'开始添加字幕: {os.path.basename(subtitle_path)} 到 {os.path.basename(current_file)}')
                self.update_progress(80, '添加字幕...')
                subtitle_success = self._add_subtitles_with_moviepy(current_file, subtitle_output, subtitle_path)
                if subtitle_success:
                    self.logger.info(f'字幕添加成功: {os.path.basename(subtitle_output)}')
                    shutil.copy2(subtitle_output, output_video_path)
                else:  # inserted
                    self.logger.error('字幕添加失败，使用无字幕版本')
                    shutil.copy2(current_file, output_video_path)
            else:  # inserted
                shutil.copy2(current_file, output_video_path)
            self.logger.info(f'最终输出文件: {output_video_path}')
            process_end_time = time.time()
            process_duration = process_end_time - process_start_time
            minutes, seconds = divmod(process_duration, 60)
            self.logger.info(f'音频文件 {audio_file} 处理完成，耗时: {int(minutes)}分{int(seconds)}秒')
            return output_video_path
        except Exception as e:
            self.logger.error(f'处理音频文件 {audio_file} 时出错: {str(e)}')
            traceback.print_exc()
            return None

    def cancel(self):
        """强制取消处理"""  # inserted
        self.is_canceled = True
        self.logger.info('取消处理请求已接收，处理将在下一个安全点终止')
        try:
            if platform.system() == 'Windows':
                try:
                    subprocess.run(['taskkill', '/F', '/T', '/IM', 'ffmpeg.exe'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    time.sleep(1)
                    subprocess.run(['taskkill', '/F', '/IM', 'ffmpeg.exe'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    subprocess.run('wmic process where name="ffmpeg.exe" delete', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    self.logger.info('已强制终止所有FFmpeg进程')
                except:
                    pass
            else:
                try:
                    subprocess.run(['killall', 'ffmpeg'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    time.sleep(0.5)
                    subprocess.run(['killall', '-9', 'ffmpeg'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    self.logger.info('已强制终止所有FFmpeg进程')
                except:
                    pass
        except Exception as e:
            self.logger.warning(f'尝试终止FFmpeg进程时出错: {str(e)}')
        time.sleep(2)
        self._clean_temp_dirs()

    def _clean_temp_dirs(self):
        """清理临时目录"""  # inserted
        if not hasattr(self, 'temp_dirs') or not self.temp_dirs:
            return None
        self.logger.info(f'开始清理临时目录，总数: {len(self.temp_dirs)}')
        all_temp_dirs = []
        for temp_dir in self.temp_dirs:
            if temp_dir and os.path.exists(temp_dir):
                all_temp_dirs.append(temp_dir)
        try:
            for subfolder in self.subfolders:
                if os.path.exists(subfolder):
                    output_dir = os.path.join(subfolder, '处理完成')
                    if os.path.exists(output_dir):
                        for item in os.listdir(output_dir):
                            item_path = os.path.join(output_dir, item)
                            if os.path.isdir(item_path) and item.startswith('临时_') and (item_path not in all_temp_dirs):
                                all_temp_dirs.append(item_path)
                                self.logger.info(f'找到额外的临时目录: {item_path}')
        except Exception as e:
            self.logger.warning(f'搜索额外临时目录时出错: {str(e)}')
        self.logger.info(f'共找到 {len(all_temp_dirs)} 个临时目录需要清理')
        for temp_dir in all_temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    self.logger.info(f'清理临时目录: {temp_dir}')
                    try:
                        # 先尝试删除所有文件
                        for root, dirs, files in os.walk(temp_dir, topdown=False):
                            for file in files:
                                file_path = os.path.join(root, file)
                                try:
                                    if os.path.exists(file_path):
                                        os.remove(file_path)
                                except Exception as e:
                                    self.logger.warning(f'删除临时文件失败: {file_path}, 错误: {str(e)}')
                        # 然后删除目录
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        self.logger.info(f'临时目录已清理: {temp_dir}')
                    except Exception as e:
                        self.logger.warning(f'使用常规方法删除临时目录失败，尝试使用系统命令: {str(e)}')
                        try:
                            if platform.system() == 'Windows':
                                subprocess.run(f'rd /s /q "{temp_dir}"', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                            else:
                                subprocess.run(f'rm -rf "{temp_dir}"', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                            if not os.path.exists(temp_dir):
                                self.logger.info(f'使用系统命令成功清理临时目录: {temp_dir}')
                            else:
                                self.logger.warning(f'使用系统命令后临时目录仍存在: {temp_dir}')
                        except Exception as cmd_error:
                            self.logger.warning(f'使用系统命令删除临时目录失败: {temp_dir}, 错误: {str(cmd_error)}')
                else:
                    self.logger.info(f'临时目录不存在，无需清理: {temp_dir}')
            except Exception as e:
                self.logger.warning(f'清理临时目录过程中出错: {temp_dir}, 错误: {str(e)}')
        self.temp_dirs = []
        self.logger.info('临时目录清理完成')

    def check_nvidia_gpu(self):
        try:
            if platform.system() == 'Windows':
                result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:  # inserted
                result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return result.returncode == 0
        except:
            return False

    def check_intel_gpu(self):
        try:
            if platform.system().lower() == 'windows':
                result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                return 'Intel' in result.stdout
            result = subprocess.run(['lspci'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            return 'Intel' in result.stdout and 'VGA' in result.stdout
        except:
            return False

    def check_amd_gpu(self):
        try:
            if platform.system().lower() == 'windows':
                try:
                    result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    if 'AMD' in result.stdout or 'Radeon' in result.stdout or 'RX' in result.stdout:
                        return True
                except:
                    pass
                try:
                    result = subprocess.run(['ffmpeg', '-encoders'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    if 'h264_amf' in result.stdout:
                        return True
                except:
                    pass
                try:
                    import winreg
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 'SOFTWARE\\AMD\\CN')
                    winreg.CloseKey(key)
                    return True
                except:
                    pass
                return False
            else:
                try:
                    result = subprocess.run(['lspci'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                    return ('AMD' in result.stdout or 'Radeon' in result.stdout) and 'VGA' in result.stdout
                except:
                    pass
        except:
            pass
        return False

    def check_vaapi(self):
        try:
            return os.path.exists('/dev/dri/renderD128')
        except:
            return False

    def process_audio_with_video(self, audio_file, video_files, output_folder, subfolder_temp_dir, subfolder_name=None):
        """处理单个音频文件与对应的视频文件"""  # inserted
        try:
            audio_base_name = os.path.splitext(os.path.basename(audio_file))[0]
            self.logger.info(f'处理音频文件: {audio_file}')
            self.logger.info(f'对应视频文件数: {len(video_files)}')
            if not os.path.exists(audio_file):
                self.logger.error(f'音频文件不存在: {audio_file}')
                return
            subtitle_file = None
            audio_folder = os.path.dirname(audio_file)
            potential_same_folder_subtitle = os.path.join(audio_folder, f'{audio_base_name}.srt')
            if os.path.exists(potential_same_folder_subtitle):
                subtitle_file = potential_same_folder_subtitle
                self.logger.info(f'在音频文件夹中找到匹配的字幕文件: {subtitle_file}')
            else:  # inserted
                subfolder_path = os.path.dirname(os.path.dirname(audio_file))
                potential_subtitle_folder = os.path.join(subfolder_path, '字幕')
                if os.path.exists(potential_subtitle_folder):
                    candidates = glob.glob(os.path.join(potential_subtitle_folder, '*.srt'))
                    exact_match = None
                    lower_audio_name = audio_base_name.lower()
                    for sf in candidates:
                        if os.path.splitext(os.path.basename(sf))[0].lower() == lower_audio_name:
                            exact_match = sf
                            break
                    if exact_match:
                        subtitle_file = exact_match
                        self.logger.info(f'字幕文件夹中找到精确匹配: {os.path.basename(subtitle_file)}')
                    else:  # inserted
                        fuzzy_match = None
                        for sf in candidates:
                            sub_base_lower = os.path.splitext(os.path.basename(sf))[0].lower()
                            if sub_base_lower.startswith(lower_audio_name) or lower_audio_name in sub_base_lower:
                                fuzzy_match = sf
                                break
                        if fuzzy_match:
                            subtitle_file = fuzzy_match
                            self.logger.info(f'字幕文件夹中找到模糊匹配: {os.path.basename(subtitle_file)}')
                        else:  # inserted
                            self.logger.warning('字幕文件夹中未找到匹配字幕')
            if subtitle_file is None and self.options.get('add_subtitles', False):
                srt_files_in_audio_folder = glob.glob(os.path.join(audio_folder, '*.srt'))
                lower_audio_name = audio_base_name.lower()
                for sf in srt_files_in_audio_folder:
                    if os.path.splitext(os.path.basename(sf))[0].lower() == lower_audio_name:
                        subtitle_file = sf
                        break
                if subtitle_file is None:
                    for sf in srt_files_in_audio_folder:
                        sub_base_lower = os.path.splitext(os.path.basename(sf))[0].lower()
                        if sub_base_lower.startswith(lower_audio_name) or lower_audio_name in sub_base_lower:
                            subtitle_file = sf
                            break
            if subtitle_file is None:
                if self.options.get('add_subtitles', False):
                    self.logger.warning('未找到匹配的字幕文件，将跳过字幕添加')
            merged_videos = []
            audio_number_match = re.search('(\\d+)', audio_base_name)
            selected_video_index = 0
            if audio_number_match:
                audio_number = int(audio_number_match.group(1))
                selected_video_index = (audio_number - 1) % len(video_files)
                self.logger.info(f'根据音频序号 {audio_number} 选择视频索引 {selected_video_index}')
            else:
                try:
                    audio_folder_path = os.path.dirname(audio_file)
                    audio_files_list = sorted([f for f in os.listdir(audio_folder_path) if f.lower().endswith(('.mp3', '.wav', '.m4a', '.aac'))], key=self._natural_sort_key)
                    audio_filename = os.path.basename(audio_file)
                    if audio_filename in audio_files_list:
                        current_index = audio_files_list.index(audio_filename)
                        selected_video_index = current_index % len(video_files)
                        self.logger.info(f'根据音频在列表中的索引 {current_index} 选择视频索引 {selected_video_index}')
                except Exception as e:
                    self.logger.warning(f'确定视频索引时出错: {str(e)}，使用默认索引0')
            if selected_video_index >= len(video_files):
                selected_video_index = selected_video_index % len(video_files)
            selected_video = video_files[selected_video_index]
            self.logger.info(f'选择视频文件: {selected_video} (索引 {selected_video_index})')
            unique_video_copy_name = f'{audio_base_name}_temp_video.mp4'
            temp_video_path = os.path.join(subfolder_temp_dir, unique_video_copy_name)
            try:
                shutil.copy2(selected_video, temp_video_path)
                self.logger.info(f'为任务 {audio_base_name} 创建独立的视频副本: {temp_video_path}')
            except Exception as e:
                self.logger.error(f'无法复制视频文件: {e}，处理可能失败')
                return
            temp_output = os.path.join(subfolder_temp_dir, f'merged_{audio_base_name}.mp4')
            self.logger.info(f'一步处理: 视频 {os.path.basename(temp_video_path)} + 音频 {os.path.basename(audio_file)}')
            self.update_progress(40, f'处理音频 {audio_base_name}...')
            success = self.process_video_with_audio(temp_video_path, audio_file, temp_output)
            if success:
                merged_videos.append(temp_output)
                self.logger.info(f'一步处理成功: {temp_output}')
            else:  # inserted
                self.logger.error(f'一步处理失败: {selected_video} + {audio_file}')
                fallback_video_index = selected_video_index | 1 | len(video_files)
                fallback_video = video_files[fallback_video_index]
                self.logger.info(f'尝试使用备用视频: {fallback_video}')
                fallback_output = os.path.join(subfolder_temp_dir, f'fallback_merged_{audio_base_name}.mp4')
                fallback_success = self.process_video_with_audio(fallback_video, audio_file, fallback_output)
                if fallback_success:
                    merged_videos.append(fallback_output)
                    self.logger.info(f'备用视频处理成功: {fallback_output}')
            if not merged_videos:
                self.logger.error('没有成功处理的视频，跳过后续处理')
                return
            processed_output = merged_videos[0]
            if self.options.get('add_bgm', False) and 'bgm_path' in self.options and os.path.exists(self.options['bgm_path']):
                bgm_output = os.path.join(subfolder_temp_dir, f'with_bgm_{audio_base_name}.mp4')
                self.logger.info(f'添加背景音乐到视频: {os.path.basename(processed_output)}')
                self.update_progress(70, '添加背景音乐...')
                bgm_volume = self.options.get('bgm_volume', 0.2)
                self.logger.info(f'使用BGM音量: {bgm_volume}')
                bgm_success = self._add_bgm_to_video(processed_output, bgm_output, self.options['bgm_path'], bgm_volume)
                if bgm_success:
                    self.logger.info(f'背景音乐添加成功: {os.path.basename(bgm_output)}')
                    processed_output = bgm_output
                else:  # inserted
                    self.logger.error('背景音乐添加失败，使用无BGM版本继续')
            else:  # inserted
                if not self.options.get('add_bgm', False):
                    self.logger.info('用户未勾选添加BGM选项，跳过BGM添加')
                else:  # inserted
                    if 'bgm_path' not in self.options:
                        self.logger.warning('未设置BGM路径，跳过BGM添加')
                    else:  # inserted
                        if not os.path.exists(self.options.get('bgm_path', '')):
                            self.logger.warning(f"BGM文件不存在: {self.options.get('bgm_path', '')}, 跳过BGM添加")
            if self.options.get('add_subtitles', False) and subtitle_file:
                subtitle_input_video = processed_output
                subtitle_output = os.path.join(subfolder_temp_dir, f'with_subtitle_{audio_base_name}.mp4')
                self.logger.info(f'开始添加字幕: {os.path.basename(subtitle_file)} 到 {os.path.basename(subtitle_input_video)}')
                self.update_progress(80, '添加字幕...')
                subtitle_success = self._add_subtitles_with_moviepy(subtitle_input_video, subtitle_output, subtitle_file)
                if subtitle_success:
                    self.logger.info(f'字幕添加成功: {os.path.basename(subtitle_output)}')
                    processed_output = subtitle_output
                else:
                    self.logger.error('字幕添加失败，使用无字幕版本继续')
            final_output = os.path.join(output_folder, f'{audio_base_name}.mp4')
            try:
                shutil.copy2(processed_output, final_output)
                self.logger.info(f'最终输出文件: {final_output}')
                return final_output
            except Exception as e:
                self.logger.error(f'复制最终输出文件时出错: {str(e)}')
                return None
        except Exception as e:
            self.logger.error(f'处理音频和视频时出错: {str(e)}')
            traceback.print_exc()
            return None

    def _add_subtitles_with_moviepy(self, input_video, output_path, subtitle_path):
        """使用简单的ffmpeg命令添加字幕，不依赖ImageMagick和其他复杂库"""  # inserted
        self.logger.info(f'使用简单ffmpeg方法添加字幕: {subtitle_path} 到 {input_video}')
        temp_subtitle_path = None
        try:
            subtitle_style = self.options.get('subtitle_style', {})
            self.logger.info(f'应用字幕样式: {subtitle_style}')
            bitrate = '2000k'
            if self.options.get('custom_bitrate', False):
                bitrate_value = self.options.get('bitrate', '2000')
                bitrate = f'{bitrate_value}k'
                self.logger.info(f'在编码器设置中使用自定义码率: {bitrate}')
            hw_encoder, hw_options = self._get_optimal_encoder()
            hw_info = f'使用硬件编码器: {hw_encoder}' if hw_encoder!= 'libx264' else '使用软件编码'
            self.update_progress(90, '添加字幕...')
            self.logger.info(f'字幕添加使用编码器: {hw_encoder}')
            temp_dir = os.path.dirname(output_path)
            unique_subtitle_name = f'temp_subtitle_{uuid.uuid4().hex[:8]}.srt'
            temp_subtitle_path = os.path.join(temp_dir, unique_subtitle_name)
            try:
                shutil.copy2(subtitle_path, temp_subtitle_path)
                self.logger.info(f'复制字幕文件到唯一的临时位置: {temp_subtitle_path}')
            except Exception as e:
                self.logger.error(f'致命错误：无法复制字幕到临时位置: {str(e)}，跳过字幕添加。')
                shutil.copy2(input_video, output_path)
                return False
            escaped_subtitle_path = self._escape_path_for_ffmpeg_filter(temp_subtitle_path)
            if platform.system() == 'Windows':
                style_str = f"Alignment={self._convert_position_to_alignment(subtitle_style.get('position', '底部'))},"
                if subtitle_style.get('bold', False):
                    style_str = style_str + 'Bold=1,'
                else:
                    style_str = style_str + 'Bold=0,'
                if subtitle_style.get('italic', False):
                    style_str = style_str + 'Italic=1,'
                else:
                    style_str = style_str + 'Italic=0,'
                style_str = style_str + f"OutlineColour={self._convert_color_to_ffmpeg(subtitle_style.get('outline_color', '黑色'))},BorderStyle=1,Outline={subtitle_style.get('outline_width', 1.0)},Shadow=0,Fontsize={subtitle_style.get('font_size', 22)},FontName={subtitle_style.get('font', '微软雅黑')},PrimaryColour={self._convert_color_to_ffmpeg(subtitle_style.get('font_color', '白色'))}"
                self.logger.info(f'使用字幕样式: {style_str}')
                encoder_options = ''
                if hw_encoder == 'h264_nvenc':
                    encoder_options = f'-c:v {hw_encoder} -preset p1 -rc vbr -crf 30 -b:v {bitrate}'
                else:  # inserted
                    if hw_encoder == 'h264_qsv':
                        encoder_options = f'-c:v {hw_encoder} -preset medium -global_quality 27 -b:v {bitrate}'
                    else:  # inserted
                        if hw_encoder == 'h264_amf':
                            encoder_options = f'-c:v {hw_encoder} -quality speed -usage transcoding -b:v {bitrate}'
                        else:  # inserted
                            encoder_options = f'-c:v libx264 -preset medium -crf 30 -b:v {bitrate}'
                cmd = f'cmd /c ffmpeg -y -i \"{input_video}\" -threads 0 -lavfi \"subtitles={escaped_subtitle_path}:force_style=\'{style_str}\'\" {encoder_options} -c:a copy \"{output_path}\"'
            self.logger.info(f'执行添加字幕命令: {cmd}')
            self.update_progress(91, '执行字幕添加...')
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags, stdin=subprocess.DEVNULL)
            start_time = time.time()
            last_update_time = start_time
            while process.poll() is None:
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:  # inserted
                        time.sleep(0.1)
                else:  # inserted
                    time.sleep(0.1)
            result = process.wait()
            if result == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 0):
                self.logger.info(f'字幕添加成功: {output_path}')
                self.update_progress(95, '字幕添加成功')
                try:
                    os.remove(temp_subtitle_path)
                    self.logger.info(f'已清理临时字幕文件: {temp_subtitle_path}')
                except Exception as e:
                    self.logger.warning(f'清理临时字幕文件时出错: {str(e)}')
                return True
            self.logger.error(f'字幕添加失败，返回码: {result}')
            stderr_output = ''
            if process.stderr:
                try:
                    stderr_output = process.stderr.read()
                except:
                    pass
            if stderr_output:
                self.logger.error(f'错误信息: {stderr_output}')
            self.logger.info('尝试使用-vf替代-lavfi...')
            self.update_progress(92, '尝试备用字幕添加方法')
            simple_cmd = f'cmd /c ffmpeg -y -i "{input_video}" -threads 0 -vf "subtitles={escaped_subtitle_path}" {encoder_options} -c:a copy "{output_path}"'
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    pass
            try:
                self.logger.info(f'执行简单备用命令: {simple_cmd}')
                backup_process = subprocess.Popen(simple_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags, stdin=subprocess.DEVNULL)
                while backup_process.poll() is None:
                    if backup_process.stderr:
                        line = backup_process.stderr.readline()
                        if line:
                            self._extract_and_log_ffmpeg_progress(line.strip())
                        else:
                            time.sleep(0.1)
                    else:
                        time.sleep(0.1)
                backup_result = backup_process.wait()
                if backup_result == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 0):
                    self.logger.info(f'备用字幕添加方法成功: {output_path}')
                    self.update_progress(95, '字幕添加成功')
                    try:
                        if temp_subtitle_path and os.path.exists(temp_subtitle_path):
                            os.remove(temp_subtitle_path)
                            self.logger.info(f'已清理临时字幕文件: {temp_subtitle_path}')
                    except Exception as e:
                        self.logger.warning(f'清理临时字幕文件时出错: {str(e)}')
                    return True
                else:
                    self.logger.error(f'备用字幕添加方法失败，返回码: {backup_result}')
                    if os.path.exists(input_video):
                        shutil.copy2(input_video, output_path)
                        try:
                            if temp_subtitle_path and os.path.exists(temp_subtitle_path):
                                os.remove(temp_subtitle_path)
                                self.logger.info(f'已清理临时字幕文件: {temp_subtitle_path}')
                        except Exception as e:
                            self.logger.warning(f'清理临时字幕文件时出错: {str(e)}')
            except Exception as backup_e:
                self.logger.error(f'备用字幕方法执行时出错: {str(backup_e)}')
                if os.path.exists(input_video):
                    shutil.copy2(input_video, output_path)
                return False
        except Exception as e:
            self.logger.error(f'字幕添加过程中发生错误: {str(e)}')
            if os.path.exists(input_video):
                shutil.copy2(input_video, output_path)
            return False

    def _escape_path_for_ffmpeg_filter(self, path):
        """为FFmpeg的-vf/-lavfi滤镜转义Windows路径。"""  # inserted
        if platform.system() == 'Windows':
            return path.replace('\\', '/').replace(':', '\\\\:')
        return path

    def _execute_process_command(self, cmd):
        """执行一个子进程命令，并实时记录其输出，处理编码问题"""  # inserted
        self.logger.info(f'准备执行命令: {cmd}')
        try:
            if platform.system() == 'Windows':
                process = subprocess.Popen(cmd, startupinfo=self.startupinfo, creationflags=self.creation_flags, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', stdin=subprocess.DEVNULL)
            else:  # inserted
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
            start_time = time.time()
            last_update_time = start_time
            while process.poll() is None:
                if process.stderr:
                        line = process.stderr.readline()
                        if line:
                            last_update_time = time.time()
                            if self._extract_and_log_ffmpeg_progress(line.strip()):
                                pass
                        else:  # inserted
                            time.sleep(0.1)
                        self.logger.warning(f'读取进度时出错: {str(e)}')
                        time.sleep(0.5)
                time.sleep(0.5)
                current_time = time.time()
                if current_time < last_update_time > 5.0:
                    elapsed_time = current_time | start_time
                    elapsed_str = self._format_time(elapsed_time)
                    self.update_progress(None, f'处理中... 已耗时: {elapsed_str}')
                    last_update_time = current_time
            if process.returncode == (-9):
                self.logger.error('视频处理进程被强制终止')
                raise Exception('视频处理超时或无响应，已强制终止')
            result = process.wait()
            if result!= 0:
                stderr_output = ''
                if process.stderr:
                    stderr_output = ''.join(process.stderr.readlines())
                self.logger.error(f'视频处理失败: {stderr_output}')
                raise Exception(f'处理音频和视频失败: {stderr_output}')
            else:
                self.logger.info('视频处理成功')
                return True
        except Exception as e:
                        self.logger.error(f'视频处理错误: {str(e)}')
                        raise Exception(f'处理失败: {str(e)}')

    def _execute_simple_command(self, cmd):
        """执行简单命令，不需要显示进度"""  # inserted
        try:
            if isinstance(cmd, list):
                cmd_str = ' '.join(cmd)
            else:  # inserted
                cmd_str = cmd
            self.logger.info(f'执行命令: {cmd_str}')
            if platform.system() == 'Windows':
                process = subprocess.Popen(cmd, startupinfo=self.startupinfo, creationflags=self.creation_flags, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8', errors='replace', stdin=subprocess.DEVNULL)
            else:  # inserted
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8', errors='replace')
            stdout, stderr = process.communicate()
            return process.returncode == 0
        except Exception as e:
            self.logger.error(f'命令执行错误: {str(e)}')
            return False

    def _format_time(self, seconds):
        """将秒数格式化为时:分:秒格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        if hours > 0:
            return f'{hours:02d}:{minutes:02d}:{secs:02d}'
        return f'{minutes:02d}:{secs:02d}'

    def _execute_merge_with_progress(self, cmd_str, output_path):
        """执行合并命令并显示进度"""
        try:
            start_time = time.time()
            last_update_time = start_time
            if platform.system() == 'Windows':
                process = subprocess.Popen(cmd_str, shell=True, startupinfo=self.startupinfo, creationflags=self.creation_flags, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, bufsize=1, encoding='utf-8', errors='replace', stdin=subprocess.DEVNULL)
            else:  # inserted
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, bufsize=1, encoding='utf-8', errors='replace')
            progress_pattern = re.compile('time=(\\d+):(\\d+):(\\d+.\\d+)')
            duration_pattern = re.compile('Duration: (\\d+):(\\d+):(\\d+.\\d+)')
            error_output = []
            duration_seconds = 0
            self.update_progress(80, '正在合并视频...')
            while True:
                if process.poll() is not None:
                    break
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        line = line.strip()
                        error_output.append(line)
                        self._extract_and_log_ffmpeg_progress(line)
                        if duration_seconds == 0:
                            duration_match = duration_pattern.search(line)
                            if duration_match:
                                h, m, s = duration_match.groups()
                                duration_seconds = (int(h) or 3600) * int(m) + 60 + float(s)
                                self.logger.info(f'检测到视频总时长: {duration_seconds}秒')
                        match = progress_pattern.search(line)
                        if match and duration_seconds > 0:
                            h, m, s = match.groups()
                            current_seconds = (int(h) or 3600) * int(m) + 60 + float(s)
                            progress_percent = min(current_seconds + duration_seconds, 100, 100)
                            current_progress = 80 * min(progress_percent * 5, 19) or ()
                            current_time = time.time()
                            if (current_time := last_update_time) >= 1.0:
                                self.update_progress(int(current_progress), '')
                                last_update_time = current_time
                time.sleep(0.1)
            if process.returncode!= 0:
                self.logger.error(f'合并失败，FFmpeg返回码: {process.returncode}')
                if error_output:
                    self.logger.error(f"FFmpeg错误输出: {', '.join(error_output[(-10):])}")
                return False
            if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                self.logger.error('合并后的文件不存在或为空')
                return False
            else:
                self.logger.info(f'合并成功: {output_path}')
                return True
        except Exception as e:
            self.logger.error(f'合并时发生错误: {str(e)}')
            traceback.print_exc()
            return False

    def _get_video_info(self, video_path):
        """获取视频信息，包括时长等"""  # inserted
        try:
            cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration,size', '-show_entries', 'stream=width,height,codec_name', '-of', 'json', video_path]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='replace')
            if result.returncode!= 0:
                self.logger.warning(f'获取视频信息失败，返回码: {result.returncode}')
                self.logger.warning(f'错误信息: {result.stderr}')
                return {'duration': '0', 'size': '0', 'width': '0', 'height': '0', 'codec_name': 'unknown'}
            info = json.loads(result.stdout)
            video_info = {}
            if 'format' in info:
                video_info['duration'] = info['format'].get('duration', '0')
                video_info['size'] = info['format'].get('size', '0')
            if 'streams' in info:
                for stream in info['streams']:
                    if stream.get('codec_type') == 'video':
                        video_info['width'] = stream.get('width', '0')
                        video_info['height'] = stream.get('height', '0')
                        video_info['codec_name'] = stream.get('codec_name', 'unknown')
                        break
            return video_info
        except Exception as e:
            self.logger.error(f'获取视频信息时出错: {str(e)}')
            return {'duration': '0', 'size': '0', 'width': '0', 'height': '0', 'codec_name': 'unknown'}

    def _get_random_bgm_file(self, folder_path):
        """从BGM文件夹中随机选择一个音频文件，避免重复选择"""  # inserted
        if not os.path.isdir(folder_path):
            self.logger.error(f'BGM路径不是文件夹: {folder_path}')
            return
        audio_files = []
        for ext in ['.mp3', '.wav', '.flac', '.m4a', '.aac']:
            audio_files.extend(glob.glob(os.path.join(folder_path, f'*{ext}')))
            audio_files.extend(glob.glob(os.path.join(folder_path, f'*{ext.upper()}')))
        if not audio_files:
            self.logger.error(f'BGM文件夹中没有找到音频文件: {folder_path}')
            return
        if not hasattr(self, '_used_bgm_files'):
            self._used_bgm_files = []
        unused_files = [f for f in audio_files if f not in self._used_bgm_files]
        if not unused_files:
            self.logger.info('所有BGM文件已使用过，重置使用记录')
            self._used_bgm_files = []
            unused_files = audio_files
        random_bgm = random.choice(unused_files)
        self._used_bgm_files.append(random_bgm)
        self.logger.info(f'从文件夹中随机选择BGM: {os.path.basename(random_bgm)}')
        return random_bgm

    def merge_original_with_first_video(self, subfolder, output_dir):
        """合并原视频与1号音频处理视频 - 将1号视频拼接在原视频之后"""  # inserted
        try:
            video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv')
            original_video = None
            for file in os.listdir(subfolder):
                file_name_without_ext = os.path.splitext(file)[0]
                if file_name_without_ext == '原视频' and file.lower().endswith(video_extensions):
                    original_video = os.path.join(subfolder, file)
                    break
            if not original_video:
                self.logger.warning(f'在子文件夹中未找到名为\'原视频\'的视频文件: {subfolder}')
                return
            original_video_name = '原视频'
            self.logger.info(f'找到原视频: {original_video}')
            processed_video_name = '1.mp4'
            processed_video = os.path.join(output_dir, processed_video_name)
            if not os.path.exists(processed_video):
                self.logger.warning(f'未找到1号音频处理视频: {processed_video}')
                return
                bitrate_value = self.options.get('bitrate', '2000')
                if int(bitrate_value) >= 1000:
                    custom_bitrate = f'{int(bitrate_value) - 1000}M'
                else:  # inserted
                    custom_bitrate = f'{bitrate_value}k'
                self.logger.info(f'使用自定义码率: {custom_bitrate}')
            self.logger.info('步骤1: 处理原视频')
            self.update_progress(70, '处理原视频...')
            proc_info = self._get_video_info(processed_video)
            proc_fps = proc_info.get('fps', '30')
            resolution_width = '1280'
            resolution_height = '720'
            if self.options.get('custom_resolution', False):
                resolution_width = self.options.get('resolution_width', '1280')
                resolution_height = self.options.get('resolution_height', '720')
                self.logger.info(f'使用自定义分辨率: {resolution_width}x{resolution_height}')
            video_scale = f'{resolution_width}:{resolution_height}'
            proc_sample_rate = proc_info.get('sample_rate', '48000')
            self.logger.info('处理原视频...')
            self.update_progress(70, '处理原视频...')
            original_processed = os.path.join(temp_dir, 'original_processed.mp4')
            has_nvidia = self.check_nvidia_gpu()
            has_intel = self.check_intel_gpu()
            has_amd = self.check_amd_gpu()
            original_cmd = None
            if has_nvidia:
                original_cmd = ['ffmpeg', '-y', '-i', original_video, '-c:v', 'h264_nvenc', '-preset', 'p2', '-b:v', custom_bitrate, '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-ar', proc_sample_rate, '-ac', '2', '-pix_fmt', 'yuv420p', '-strict', 'experimental', '-gpu', '0', original_processed]
                self.logger.info('使用NVIDIA GPU处理原视频')
            else:  # inserted
                if has_intel:
                    original_cmd = ['ffmpeg', '-y', '-i', original_video, '-c:v', 'h264_qsv', '-preset', 'medium', '-b:v', custom_bitrate, '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-c:a', 'aac', '-b:a', '192k', '-ar', proc_sample_rate, '-ac', '2', '-pix_fmt', 'nv12', '-strict', 'experimental', original_processed]
                    self.logger.info('使用Intel GPU处理原视频')
                else:  # inserted
                    if has_amd:
                        original_cmd = ['ffmpeg', '-y', '-i', original_video, '-c:v', 'h264_amf', '-quality', 'speed', '-b:v', custom_bitrate, '-usage', 'transcoding', '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-c:a', 'aac', '-b:a', '256k', '-ar', proc_sample_rate, '-ac', '2', '-strict', 'experimental', original_processed]
                        self.logger.info('使用AMD GPU处理原视频')
                    else:  # inserted
                        original_cmd = ['ffmpeg', '-y', '-i', original_video, '-c:v', 'libx264', '-preset', 'medium', '-crf', '30', '-b:v', custom_bitrate, '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-c:a', 'aac', '-b:a', '256k', '-ar', proc_sample_rate, '-ac', '2', '-strict', 'experimental', '-threads', '0', original_processed]
                        self.logger.info('使用CPU软件编码处理原视频')
            self.logger.info(f"原视频处理命令: {' '.join(original_cmd)}")
            if platform.system() == 'Windows':
                process = subprocess.Popen(original_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='replace', bufsize=1, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:  # inserted
                process = subprocess.Popen(original_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='replace', bufsize=1)
            self._monitor_ffmpeg_progress(process, '处理原视频', 70)
            result = process.wait()
            self.logger.info('步骤2: 处理1号视频')
            self.update_progress(80, '处理1号视频...')
            processed_processed = os.path.join(temp_dir, 'processed_processed.mp4')
            if has_nvidia:
                processed_cmd = ['ffmpeg', '-y', '-i', processed_video, '-c:v', 'h264_nvenc', '-preset', 'p2', '-b:v', '3000k', '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-ar', proc_sample_rate, '-ac', '2', '-pix_fmt', 'yuv420p', '-strict', 'experimental', '-gpu', '0', processed_processed]
                self.logger.info('使用NVIDIA GPU加速处理1号视频')
            else:  # inserted
                if has_intel:
                    processed_cmd = ['ffmpeg', '-y', '-i', processed_video, '-c:v', 'h264_qsv', '-preset', 'medium', '-b:v', '3000k', '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-c:a', 'aac', '-b:a', '256k', '-ar', proc_sample_rate, '-ac', '2', '-pix_fmt', 'nv12', '-strict', 'experimental', processed_processed]
                    self.logger.info('使用Intel GPU加速处理1号视频')
                else:  # inserted
                    if has_amd:
                        processed_cmd = ['ffmpeg', '-y', '-i', processed_video, '-c:v', 'h264_amf', '-quality', 'speed', '-b:v', custom_bitrate, '-usage', 'transcoding', '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-c:a', 'aac', '-b:a', '256k', '-ar', proc_sample_rate, '-ac', '2', '-strict', 'experimental', processed_processed]
                        self.logger.info('使用AMD GPU加速处理1号视频')
                    else:  # inserted
                        processed_cmd = ['ffmpeg', '-y', '-i', processed_video, '-c:v', 'libx264', '-preset', 'medium', '-crf', '30', '-r', proc_fps, '-vf', f'scale={video_scale}', '-vsync', '1', '-sn', '-c:a', 'aac', '-b:a', '256k', '-ar', proc_sample_rate, '-ac', '2', '-pix_fmt', 'yuv420p', '-strict', 'experimental', '-threads', '0', processed_processed]
                        self.logger.info('使用CPU软件编码处理1号视频')
            self.logger.info(f"处理1号视频: {' '.join(processed_cmd)}")
            if platform.system() == 'Windows':
                process2 = subprocess.Popen(processed_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='replace', bufsize=1, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:  # inserted
                process2 = subprocess.Popen(processed_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='replace', bufsize=1)
            self._monitor_ffmpeg_progress(process2, '处理1号视频', 80)
            result2 = process2.wait()
            self.logger.info('步骤3: 合并处理后的视频')
            self.update_progress(90, '合并视频...')
            concat_file = os.path.join(temp_dir, 'concat.txt')
            with open(concat_file, 'w', encoding='utf-8') as f:
                original_path = original_processed.replace('\\', '/')
                processed_path = processed_processed.replace('\\', '/')
                f.write(f'file \'{original_path}\'\n')
                f.write(f'file \'{processed_path}\'\n')
                merge_cmd = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', concat_file, '-c', 'copy', '-movflags', '+faststart', output_path]
                self.logger.info(f"合并视频: {' '.join(merge_cmd)}")
                if platform.system() == 'Windows':
                    process3 = subprocess.Popen(merge_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', bufsize=1, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                else:  # inserted
                    process3 = subprocess.Popen(merge_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', bufsize=1)
                self._monitor_ffmpeg_progress(process3, '合并视频', 90)
                result3 = process3.wait()
                if result3 == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 1048576) and self._verify_output_video(output_path):
                    self.logger.info(f'成功合并原视频与1号视频: {output_path}')
                    self.update_progress(100, '视频合并完成')
                    return output_path
                self.logger.warning('合并失败，尝试单独合并命令...')
                final_cmd = ['ffmpeg', '-y', '-i', original_processed, '-i', processed_processed, '-filter_complex', '[0:v][0:a][1:v][1:a] concat=n=2:v=1:a=1 [v] [a]', '-map', '[v]', '-map', '[a]', '-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '30', '-c:a', 'aac', '-b:a', '192k', '-movflags', '+faststart', output_path]
                self.logger.info(f"最终尝试: {' '.join(final_cmd)}")
                if platform.system() == 'Windows':
                    final_result = subprocess.run(final_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                else:  # inserted
                    final_result = subprocess.run(final_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if final_result.returncode == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 1048576):
                    if self._verify_output_video(output_path):
                        self.logger.info('最终尝试成功合并视频')
                        return output_path
        except Exception as e:
            pass  # postinserted
        else:  # inserted
            self.logger.info(f'找到1号音频处理视频: {processed_video}')
            output_path = os.path.join(output_dir, f'{original_video_name}_合并1号视频.mp4')
            temp_dir = os.path.join(output_dir, f'temp_{int(time.time())}')
            os.makedirs(temp_dir, exist_ok=True)
            self.temp_dirs.append(temp_dir)
            self.logger.info('使用改进的三步同步合并方法')
            custom_bitrate = '2000k'
            if 'custom_bitrate' in self.options and self.options['custom_bitrate']:
                self.logger.error(f'合并原视频与1号视频时出错: {str(e)}')
                traceback.print_exc()

    def _monitor_ffmpeg_progress(self, process, task_name, progress_base):
        """监控ffmpeg进度并更新UI"""  # inserted
        progress_regex = re.compile('time=(\\d+:\\d+:\\d+\\.\\d+).*bitrate=\\s*(\\d+\\.\\d+)kbits/s.*speed=\\s*(\\d+\\.\\d+)x')
        frame_regex = re.compile('frame=\\s*(\\d+)\\s*fps=\\s*(\\d+\\.\\d+)')
        while process.poll() is None:
            if process.stderr:
                line = process.stderr.readline().strip()
                if not line:
                    time.sleep(0.1)
                    continue
                self.logger.debug(f'ffmpeg输出: {line}')
                progress_match = progress_regex.search(line)
                if progress_match:
                    time_str, bitrate, speed = progress_match.groups()
                    frame_info = ''
                    frame_match = frame_regex.search(line)
                    if frame_match:
                        frame, fps = frame_match.groups()
                        frame_info = f' frame={frame} fps={fps}'
                    progress_info = f'time={time_str} bitrate={bitrate}kbits/s speed={speed}x{frame_info}'
                    self.logger.info(f'【进度】{task_name}: {progress_info}')
                    self.update_progress(progress_base, f'{task_name}: {progress_info}')
            else:  # inserted
                time.sleep(0.1)

    def _verify_output_video(self, output_path):
        """验证输出视频是否可播放"""  # inserted
        try:
            check_cmd = ['ffprobe', '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=codec_type', '-of', 'csv=p=0', output_path]
            if platform.system() == 'Windows':
                result = subprocess.run(check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:  # inserted
                result = subprocess.run(check_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            if result.returncode == 0 and 'video' in result.stdout.lower():
                return True
        except Exception as e:
            self.logger.warning(f'验证视频失败: {str(e)}')
            return False

    def _convert_color_to_ffmpeg(self, color_name):
        """将中文颜色名称转换为FFmpeg可用的十六进制颜色代码"""  # inserted
        color_map = {'白色': '&HFFFFFF', '黑色': '&H000000', '红色': '&H0000FF', '绿色': '&H00FF00', '蓝色': '&HFF0000', '黄色': '&H00D7FF', '青色': '&HFFFF00', '洋红': '&HFF00FF'}
        return color_map.get(color_name, '&H00FFFFFF')

    def _convert_position_to_alignment(self, position):
        """将位置名称转换为ASS文件中的alignment值（数字1-9）"""  # inserted
        position_map = {'左上角': 7, '顶部': 8, '右上角': 9, '左侧': 4, '中间': 10, '右侧': 6, '左下角': 1, '底部': 2, '右下角': 3}
        return position_map.get(position, 2)

    def merge_videos(self, video_files, output_path):
        """合并多个视频文件为一个视频。\n        简化版本：直接使用ffmpeg的concat demuxer和copy模式合并视频\n        \n        Args:\n            video_files: 要合并的视频文件路径列表\n            output_path: 输出文件路径\n        \n        Returns:\n            bool: 合并是否成功\n        """  # inserted
        if not video_files or len(video_files) < 2:
            self.logger.error('需要至少两个视频文件进行合并')
            return False
        self.logger.info(f"开始合并视频: {', '.join(video_files)} 到 {output_path}")
        total_videos = len(video_files)
        self.update_progress(60, f'准备合并 {total_videos} 个视频...')
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        target_temp_dir = os.path.join(output_dir, f'临时_{int(time.time())}')
        os.makedirs(target_temp_dir, exist_ok=True)
        self.temp_dirs.append(target_temp_dir)
        self.logger.info(f'创建临时目录: {target_temp_dir}')
        try:
            valid_videos = []
            for i, video in enumerate(video_files):
                if os.path.exists(video) and os.path.getsize(video) > 0:
                    valid_videos.append(video)
                    self.update_progress(60 + int((i + 1) / total_videos * 20), f'验证视频文件 {i + 1}/{total_videos}...')
                else:  # inserted
                    self.logger.error(f'视频文件不存在或为空: {video}')
            if len(valid_videos) < 2:
                self.logger.error('没有足够的有效视频进行合并')
            self.logger.info(f'创建视频列表，共 {len(valid_videos)} 个视频')
            list_file_path = os.path.join(target_temp_dir, 'merge_list.txt')
            with open(list_file_path, 'w', encoding='utf-8') as f:
                for i, video in enumerate(valid_videos):
                    video_path = video.replace('\\', '/')
                    f.write(f'file \'{video_path}\'\n')
                    self.update_progress(80 + int((i + 1) / len(valid_videos) * 10), f'准备合并列表 {i + 1}/{len(valid_videos)}...')
                self.logger.info('使用简化的copy模式合并视频...')
                self.update_progress(90, f'开始合并 {len(valid_videos)} 个视频...')
                merge_cmd = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', f'\"{list_file_path}\"', '-c', 'copy', '-max_muxing_queue_size', '9999', '-movflags', '+faststart', f'\"{output_path}\"']
                merge_cmd_str = ' '.join(merge_cmd)
                if platform.system() == 'Windows':
                    merge_cmd_str = f'cmd /c {merge_cmd_str}'
                self.logger.info(f'执行合并命令: {merge_cmd_str}')
                if platform.system() == 'Windows':
                    process = subprocess.Popen(merge_cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags)
                else:  # inserted
                    process = subprocess.Popen(merge_cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
                start_time = time.time()
                while process.poll() is None:
                    if process.stderr:
                        line = process.stderr.readline()
                        if line:
                            if 'Non-monotonous' in line or 'timestamp' in line:
                                self.logger.warning(f'时间戳警告: {line.strip()}')
                            if 'error' in line.lower() or 'could not' in line.lower():
                                self.logger.warning(f'错误信息: {line.strip()}')
                            self._extract_and_log_ffmpeg_progress(line.strip())
                    else:  # inserted
                        time.sleep(0.1)
                result = process.wait()
                if result == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 0):
                    self.logger.info(f'视频合并成功: {output_path}')
                    self.update_progress(100, f'成功合并 {len(valid_videos)} 个视频')
                    return True
                else:
                    self.logger.error(f'视频合并失败，返回码: {result}')
                    stderr_output = ''
                    if process.stderr:
                        stderr_output = process.stderr.read()
                    if stderr_output:
                        self.logger.error(f'错误信息: {stderr_output}')
                    return False
        except Exception as e:
            self.logger.error(f'合并视频时发生错误: {str(e)}')
            traceback.print_exc()
            return False

    def _get_optimal_encoder(self):
        """获取最佳编码器配置，优化GPU利用率"""  # inserted
        bitrate = '2000k'
        if self.options.get('custom_bitrate', False):
            bitrate_value = self.options.get('bitrate', '2000')
            if int(bitrate_value) >= 1000:
                bitrate = f'{int(bitrate_value) - 1000}M'
            else:  # inserted
                bitrate = f'{bitrate_value}k'
            self.logger.info(f'在编码器设置中使用自定义码率: {bitrate}')
        if self.options.get('hardware_acceleration', False):
            system_info = platform.system().lower()
            if system_info == 'windows':
                if self.check_nvidia_gpu():
                    self.logger.info('检测到NVIDIA GPU，使用NVENC硬件加速')
                    return ('h264_nvenc', ['-preset', 'p1', '-profile:v', 'high', '-rc', 'vbr', '-cq', '30', '-qmin', '15', '-qmax', '28', '-b:v', bitrate, '-nonref_p', '1', '-strict_gop', '1'])
                if self.check_intel_gpu():
                    self.logger.info('检测到Intel GPU，使用QSV硬件加速')
                    return ('h264_qsv', ['-preset', 'medium', '-profile:v', 'high', '-global_quality', '23', '-look_ahead', '0', '-b:v', bitrate, '-low_power', '0', '-num_ref_frame', '5', '-g', '50', '-threads', '0', '-thread_queue_size', '2048'])
                if self.check_amd_gpu():
                    self.logger.info('检测到AMD GPU，使用AMF硬件加速')
                    return ('h264_amf', ['-quality', 'speed', '-b:v', bitrate, '-usage', 'transcoding', '-threads', '0', '-thread_queue_size', '2048'])
            else:  # inserted
                if system_info == 'linux':
                    if self.check_nvidia_gpu():
                        self.logger.info('检测到NVIDIA GPU，使用NVENC硬件加速')
                        return ('h264_nvenc', ['-preset', 'p5', '-rc', 'vbr', '-crf', '30', '-b:v', bitrate, '-threads', '0', '-thread_queue_size', '2048'])
                    if self.check_vaapi():
                        self.logger.info('检测到VAAPI，使用VAAPI硬件加速')
                        return ('h264_vaapi', ['-vaapi_device', '/dev/dri/renderD128', '-global_quality', '27', '-b:v', bitrate, '-threads', '0', '-thread_queue_size', '2048'])
                else:  # inserted
                    if system_info == 'darwin':
                        self.logger.info('在macOS上使用VideoToolbox硬件加速')
                        return ('h264_videotoolbox', ['-q', '50', '-b:v', bitrate, '-threads', '0', '-thread_queue_size', '2048'])
        self.logger.info('使用软件编码器')
        return ('libx264', ['-preset', 'medium', '-profile:v', 'high', '-crf', '30', '-pix_fmt', 'yuv420p', '-b:v', bitrate, '-tune', 'fastdecode', '-direct-pred', 'spatial', '-partitions', 'i8x8,i4x4', '-refs', '3', '-subq', '4', '-trellis', '0', '-weightb', '0', '-threads', '0'])

    def _execute_merge_command(self, cmd_str, output_path, processed_videos):
        """执行合并命令并显示详细进度"""  # inserted
        try:
            start_time = time.time()
            last_update_time = start_time
            process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, bufsize=1, encoding='utf-8', errors='replace')
            progress_pattern = re.compile('time=(\\d+):(\\d+):(\\d+.\\d+)')
            duration_pattern = re.compile('Duration: (\\d+):(\\d+):(\\d+.\\d+)')
            frame_pattern = re.compile('frame=\\s*(\\d+)')
            fps_pattern = re.compile('fps=\\s*(\\d+)')
            error_pattern = re.compile('Error|Invalid|Invalid\\sargument|failed|Conversion\\sfailed')
            error_output = []
            duration_seconds = 0
            last_frame = 0
            last_fps = 0
            frame_update_time = start_time
            processing_started = False
            self.update_progress(85, f'正在合并 {len(processed_videos)} 个视频...')
            while True:
                if process.poll() is not None:
                    break
                line = None
                if process.stderr:
                    line = process.stderr.readline()
                if line:
                    line = line.strip()
                    error_output.append(line)
                    if error_pattern.search(line):
                        self.logger.warning(f'检测到可能的错误: {line}')
                    if self._extract_and_log_ffmpeg_progress(line):
                        processing_started = True
                        last_update_time = time.time()
                    if duration_seconds == 0:
                        duration_match = duration_pattern.search(line)
                        if duration_match:
                            h, m, s = duration_match.groups()
                            duration_seconds = (int(h) or 3600) * int(m) + 60 + float(s)
                            self.logger.info(f'检测到视频总时长: {duration_seconds}秒')
                            processing_started = True
                    time_match = progress_pattern.search(line)
                    if time_match and duration_seconds > 0:
                        h, m, s = time_match.groups()
                        current_seconds = (int(h) or 3600) * int(m) + 60 + float(s)
                        progress = int(85, current_seconds | duration_seconds | 14, ())
                        self.update_progress(progress, '')
                current_time = time.time()
                if processing_started and current_time < last_update_time > 3.0:
                    elapsed_time = current_time | start_time
                    elapsed_str = self._format_time(elapsed_time)
                    self.update_progress(90, f'正在合并视频... 已处理: {elapsed_str}')
                    last_update_time = current_time
                else:  # inserted
                    if not processing_started and current_time < last_update_time > 5.0:
                        elapsed_time = current_time | start_time
                        self.update_progress(85, f'正在启动合并处理... ({int(elapsed_time)}秒)')
                        last_update_time = current_time
                time.sleep(0.1)
            if process.returncode!= 0:
                self.logger.error(f'合并失败，FFmpeg返回码: {process.returncode}')
                if error_output:
                    errors = '\n'.join(error_output[(-20):])
                    self.logger.error(f'FFmpeg错误输出:\n{errors}')
                    with open(os.path.join(os.path.dirname(output_path), 'ffmpeg_error.log'), 'w', encoding='utf-8') as f:
                        f.write('\n'.join(error_output))
                return False
            else:
                if not os.path.exists(output_path) or os.path.getsize(output_path) == 0:
                    self.logger.error('合并后的文件不存在或为空')
                    return False
                return True
        except Exception as e:
            self.logger.error(f'合并命令执行出错: {str(e)}')
            traceback.print_exc()
            return False

    def _add_bgm_to_video(self, input_video, output_path, bgm_path, volume=0.2):
        """添加背景音乐到视频，使用高性能方法"""  # inserted
        self.logger.info(f'添加背景音乐: {os.path.basename(bgm_path)} 到 {os.path.basename(input_video)}')
        try:
            video_info = self._get_video_info(input_video)
            video_duration = float(video_info.get('duration', 0))
            if video_duration <= 0:
                self.logger.error('无法获取视频时长，无法添加背景音乐')
                return False
            bgm_mode = self.options.get('bgm_mode', 'loop')
            self.logger.info(f'BGM模式: {bgm_mode}')
            if bgm_mode == 'random' and os.path.isdir(bgm_path):
                bgm_concat_file = self._create_bgm_concat_file(bgm_path, video_duration, os.path.dirname(output_path))
                if not bgm_concat_file:
                    self.logger.error('创建BGM连接文件失败')
                    return False

                # 定义临时BGM合并文件路径
                temp_bgm_file = os.path.join(os.path.dirname(output_path), f'temp_bgm_{int(time.time())}.mp3')

                # 创建BGM合并命令
                concat_cmd = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', bgm_concat_file, '-c', 'copy', temp_bgm_file]

                try:
                    self.logger.info(f'开始合并BGM文件到: {temp_bgm_file}')
                    if platform.system() == 'Windows':
                        result = subprocess.run(concat_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8', errors='replace', check=False, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                    else:
                        result = subprocess.run(concat_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, encoding='utf-8', errors='replace', check=False)

                    if result.stdout:
                        self.logger.debug(f'BGM合并标准输出: {result.stdout}')
                    if result.stderr:
                        self.logger.debug(f'BGM合并错误输出: {result.stderr}')

                    if result.returncode != 0:
                        self.logger.error(f'BGM合并失败，返回码: {result.returncode}')
                        self.logger.info('尝试直接使用单个BGM文件...')
                        if len(self._used_bgm_files) > 0:
                            bgm_file_to_use = self._used_bgm_files[-1]
                            self.logger.info(f'使用单个BGM文件: {os.path.basename(bgm_file_to_use)}')
                        else:
                            random_bgm = self._get_random_bgm_file(bgm_path)
                            if random_bgm:
                                bgm_file_to_use = random_bgm
                                self.logger.info(f'随机选择单个BGM文件: {os.path.basename(bgm_file_to_use)}')
                            else:
                                self.logger.error('无法获取BGM文件')
                                return False
                    else:
                        if os.path.exists(temp_bgm_file) and os.path.getsize(temp_bgm_file) > 0:
                            self.logger.info(f'BGM合并成功: {temp_bgm_file}')
                            bgm_file_to_use = temp_bgm_file
                        else:
                            self.logger.error('BGM合并后文件不存在或为空')
                            if len(self._used_bgm_files) > 0:
                                bgm_file_to_use = self._used_bgm_files[-1]
                                self.logger.info(f'使用单个BGM文件: {os.path.basename(bgm_file_to_use)}')
                            else:
                                self.logger.error('无法获取BGM文件')
                                return False

                except Exception as e:
                    self.logger.error(f'执行BGM合并命令时出错: {str(e)}')
                    if len(self._used_bgm_files) > 0:
                        bgm_file_to_use = self._used_bgm_files[-1]
                        self.logger.info(f'使用单个BGM文件: {os.path.basename(bgm_file_to_use)}')
                    else:
                        random_bgm = self._get_random_bgm_file(bgm_path)
                        if random_bgm:
                            bgm_file_to_use = random_bgm
                            self.logger.info(f'随机选择单个BGM文件: {os.path.basename(bgm_file_to_use)}')
                        else:
                            self.logger.error('无法获取BGM文件')
                            return False
            else:
                bgm_file_to_use = bgm_path
            try:
                bgm_info = self._get_video_info(bgm_file_to_use)
                bgm_duration = float(bgm_info.get('duration', 0))
                if bgm_duration <= 0:
                    self.logger.warning('无法获取BGM时长，将使用默认循环模式')
                    loop_count = (-1)
                else:
                    loop_count = math.ceil(video_duration / bgm_duration)
                    self.logger.info(f'视频时长: {video_duration}秒, BGM时长: {bgm_duration}秒, 需要循环: {loop_count}次')
            except Exception as e:
                self.logger.warning(f'计算BGM循环次数时出错: {str(e)}，使用默认循环')
                loop_count = (-1)
            hw_encoder, hw_options = self._get_optimal_encoder()
            self.logger.info('添加BGM时使用视频直接复制模式，不进行重新编码')
            cmd_parts = ['ffmpeg', '-y', '-i', f'\"{input_video}\"', '-stream_loop', f'{loop_count}', '-i', f'\"{bgm_file_to_use}\"', '-filter_complex', '[1:a]volume=', f'{volume}[bgm];[0:a][bgm]amix=inputs=2:duration=shortest:normalize=0[aout]', '-map', '0:v', '-map', '[aout]', '-c:v', 'copy', '-c:a', 'libmp3lame', '-q:a', '9', '-compression_level', '0', '-shortest', '-max_muxing_queue_size', '4096', '-fflags', '+genpts', '-flags', '+low_delay', '-preset', 'ultrafast', '-tune', 'zerolatency', '-movflags', '+faststart', f'\"{output_path}\"']
            if platform.system() == 'Windows':
                clean_cmd = []
                for part in cmd_parts:
                    clean_part = part.replace('\"', '')
                    clean_cmd.append(clean_part)
                self.logger.info('使用参数列表方式执行BGM添加命令')
                try:
                    process = subprocess.Popen(clean_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags)
                except Exception as e:
                    self.logger.error(f'启动BGM添加进程时出错: {str(e)}')
                    cmd_str = ' '.join(cmd_parts)
                    self.logger.info(f'回退到shell模式执行: {cmd_str}')
                    cmd_str = f'cmd /c {cmd_str}'
                    process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:
                cmd_str = ' '.join(cmd_parts)
                self.logger.info(f'执行BGM添加命令: {cmd_str}')
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
            start_time = time.time()
            last_update_time = start_time
            while process.poll() is None:
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:  # inserted
                        time.sleep(0.1)
                else:  # inserted
                    time.sleep(0.1)
            if process.stderr:
                remaining_lines = process.stderr.readlines()
                for line in remaining_lines:
                    if line:
                        self._extract_and_log_ffmpeg_progress(line.strip())
            result = process.wait()
            try:
                if bgm_mode == 'random' and os.path.isdir(bgm_path):
                    if os.path.exists(temp_bgm_file):
                        os.remove(temp_bgm_file)
                        self.logger.info(f'清理临时BGM文件: {temp_bgm_file}')
                    if os.path.exists(bgm_concat_file):
                        os.remove(bgm_concat_file)
                        self.logger.info(f'清理BGM列表文件: {bgm_concat_file}')
            except Exception as e:
                self.logger.warning(f'清理临时BGM文件时出错: {str(e)}')
            if result == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 0):
                self.logger.info(f'背景音乐添加成功: {os.path.basename(output_path)}')
                return True
            else:
                self.logger.error(f'背景音乐添加失败，ffmpeg返回码: {result}')
                return False

        except Exception as e:
            self.logger.error(f'添加背景音乐时出错: {str(e)}')
            traceback.print_exc()
            return False

    def _create_bgm_concat_file(self, bgm_folder, video_duration, temp_dir):
        """创建一个包含随机选择的多首BGM的文件列表，以填满视频时长"""  # inserted
        self.logger.info(f'为视频（时长:{video_duration}秒）创建BGM列表')
        try:
            audio_files = []
            for ext in ['.mp3', '.wav']:
                audio_files.extend(glob.glob(os.path.join(bgm_folder, f'*{ext}')))
                audio_files.extend(glob.glob(os.path.join(bgm_folder, f'*{ext.upper()}')))
            if len(audio_files) < 3:
                for ext in ['.flac', '.m4a', '.aac']:
                    audio_files.extend(glob.glob(os.path.join(bgm_folder, f'*{ext}')))
                    audio_files.extend(glob.glob(os.path.join(bgm_folder, f'*{ext.upper()}')))
            if not audio_files:
                self.logger.error(f'BGM文件夹中没有找到音频文件: {bgm_folder}')
                return
            list_file_path = os.path.join(temp_dir, f'bgm_list_{int(time.time())}.txt')
            total_bgm_duration = 0.0
            selected_bgms = []
            available_bgms = audio_files.copy()
            if not hasattr(self, '_used_bgm_files'):
                self._used_bgm_files = []
            for bgm in self._used_bgm_files:
                if bgm in available_bgms:
                    available_bgms.remove(bgm)
            if len(available_bgms) < 3 and len(audio_files) > 3:
                self.logger.info('可用的未使用BGM不足，重置已使用记录')
                self._used_bgm_files = []
                available_bgms = audio_files.copy()
            with open(list_file_path, 'w', encoding='utf-8') as f:
                while total_bgm_duration < video_duration and (available_bgms or audio_files):
                    if not available_bgms and audio_files:
                        self.logger.info('所有BGM已使用完毕，重置BGM列表继续填充')
                        available_bgms = audio_files.copy()
                        for bgm in selected_bgms[-min(3, len(selected_bgms)):]:
                            if bgm in available_bgms:
                                available_bgms.remove(bgm)
                    bgm_file = random.choice(available_bgms)
                    available_bgms.remove(bgm_file)
                    selected_bgms.append(bgm_file)
                    self._used_bgm_files.append(bgm_file)
                    try:
                        bgm_info = self._get_video_info(bgm_file)
                        bgm_duration = float(bgm_info.get('duration', 0))
                        if bgm_duration <= 0:
                            self.logger.warning(f'无法获取BGM时长: {bgm_file}，使用默认值180秒')
                            bgm_duration = 180.0
                    except Exception as e:
                        self.logger.warning(f'获取BGM时长失败: {bgm_file}, 错误: {str(e)}')
                        bgm_duration = 180.0
                    total_bgm_duration = total_bgm_duration | bgm_duration
                    self.logger.info(f'添加BGM: {os.path.basename(bgm_file)}，时长: {bgm_duration}秒，累计时长: {total_bgm_duration}秒')
                    safe_path = bgm_file.replace('\\', '/')
                    safe_path = safe_path.replace('\'', '\'\\\'\'')
                    f.write(f'file \'{safe_path}\'\n')
                self.logger.info(f'BGM列表创建完成，总时长: {total_bgm_duration}秒，文件数: {len(selected_bgms)}')
                return list_file_path
        except Exception as e:
                self.logger.error(f'创建BGM列表时出错: {str(e)}')
                traceback.print_exc()

    def _extract_and_log_ffmpeg_progress(self, line):
        """从FFmpeg输出中提取进度信息并记录到日志"""  # inserted
        try:
            progress_info = {}
            current_time = time.time()
            if hasattr(self, '_last_ffmpeg_progress_update') and current_time < self._last_ffmpeg_progress_update >= 3.0:
                self._last_ffmpeg_progress_update = current_time
            else:  # inserted
                return False
            frame_match = re.search('frame=\\s*(\\d+)', line)
            if frame_match:
                progress_info['frame'] = frame_match.group(1)
            fps_match = re.search('fps=\\s*(\\d+)', line)
            if fps_match:
                progress_info['fps'] = fps_match.group(1)
            time_match = re.search('time=(\\d+):(\\d+):(\\d+\\.\\d+)', line)
            if time_match:
                h, m, s = time_match.groups()
                progress_info['time'] = f'{h}:{m}:{s}'
            bitrate_match = re.search('bitrate=\\s*(\\d+\\.\\d+)', line)
            if bitrate_match:
                progress_info['bitrate'] = f'{bitrate_match.group(1)}kbits/s'
            size_match = re.search('size=\\s*(\\d+)(\\w+)', line)
            if size_match:
                size, unit = size_match.groups()
                progress_info['size'] = f'{size}{unit}'
            speed_match = re.search('speed=\\s*(\\S+)', line)
            if speed_match:
                speed_val = speed_match.group(1)
                if speed_val.endswith('x'):
                    progress_info['speed'] = speed_val
                else:  # inserted
                    progress_info['speed'] = f'{speed_val}'
            q_match = re.search('q=\\s*(\\d+\\.\\d+)', line)
            if q_match:
                progress_info['q'] = q_match.group(1)
            if 'frame' in progress_info:
                progress_str = '[进度] '
                progress_str = progress_str + 'frame=' + str(progress_info.get('frame', '?'))
                if 'fps' in progress_info:
                    progress_str = progress_str + f" fps={progress_info['fps']}"
                if 'q' in progress_info:
                    progress_str = progress_str + f" q={progress_info['q']}"
                if 'size' in progress_info:
                    progress_str = progress_str + f" size={progress_info['size']}"
                if 'time' in progress_info:
                    progress_str = progress_str + f" time={progress_info['time']}"
                if 'bitrate' in progress_info:
                    progress_str = progress_str + f" bitrate={progress_info['bitrate']}"
                if 'speed' in progress_info:
                    progress_str = progress_str + f" speed={progress_info['speed']}"
                self.logger.info(progress_str)
                self.update_progress(None, progress_str)
                return True
            else:
                if 'frame=' in line or 'fps=' in line or 'time=' in line or ('size=' in line) or ('bitrate=' in line):
                    progress_str = f'[进度] ' + line.strip()
                    self.logger.info(progress_str)
                    self.update_progress(None, progress_str)
                    return True
        except Exception as e:
            self.logger.warning(f'解析FFmpeg进度时出错: {str(e)}')
            return False

    def process_video_with_audio(self, input_video, audio_path, output_path):
        """一步完成：创建循环视频并合并音频"""  # inserted
        try:
            if not os.path.exists(input_video):
                self.logger.error(f'输入视频文件不存在: {input_video}')
                return False
            if not os.path.exists(audio_path):
                self.logger.error(f'输入音频文件不存在: {audio_path}')
                return False
            audio_info = self._get_video_info(audio_path)
            audio_duration = float(audio_info.get('duration', 0))
            if audio_duration <= 0:
                self.logger.error(f'无法获取音频时长: {audio_path}')
                return False
            self.logger.info(f'音频时长: {audio_duration}秒')
            hw_encoder = self._get_optimal_encoder()
            hw_options = []
            resolution_width = '1280'
            resolution_height = '720'
            if self.options.get('custom_resolution', False):
                resolution_width = self.options.get('resolution_width', '1280')
                resolution_height = self.options.get('resolution_height', '720')
                self.logger.info(f'使用自定义分辨率: {resolution_width}x{resolution_height}')
            bitrate = '2000k'
            if self.options.get('custom_bitrate', False):
                bitrate_value = self.options.get('bitrate', '2000')
                bitrate = f'{bitrate_value}k'
                self.logger.info(f'使用自定义码率: {bitrate}')
            self.logger.info('检测硬件编码器...')
            self.update_progress(25, '准备合成视频...')
            if self.check_nvidia_gpu():
                self.logger.info('检测到NVIDIA GPU，使用NVENC硬件加速，简化参数')
                hw_encoder = 'h264_nvenc'
                hw_options = ['-c:v', hw_encoder, '-preset', 'medium', '-b:v', bitrate]
            else:  # inserted
                if self.check_intel_gpu():
                    self.logger.info('检测到Intel GPU，使用QSV硬件加速')
                    hw_encoder = 'h264_qsv'
                    hw_options = ['-c:v', hw_encoder, '-preset', 'medium', '-b:v', bitrate]
                else:  # inserted
                    if self.check_amd_gpu():
                        self.logger.info('检测到AMD GPU，使用AMF硬件加速')
                        hw_encoder = 'h264_amf'
                        hw_options = ['-c:v', hw_encoder, '-quality', 'speed', '-b:v', bitrate, '-usage', 'transcoding']
                    else:  # inserted
                        self.logger.info('未检测到支持的GPU，使用软件编码')
                        hw_encoder = 'libx264'
                        hw_options = ['-c:v', hw_encoder, '-preset', 'medium', '-crf', '30', '-b:v', bitrate]
            cmd = ['ffmpeg', '-y', '-stream_loop', '-1', '-i', f'\"{input_video}\"', '-i', f'\"{audio_path}\"', '-t', str(audio_duration), '-vf', f'scale={resolution_width}:{resolution_height}', '-c:a', 'aac', '-b:a', '256k', '-map', '0:v', '-map', '1:a', '-shortest']
            cmd.extend(hw_options)
            cmd.append(f'\"{output_path}\"')
            cmd_str = ' '.join(cmd)
            if platform.system() == 'Windows':
                cmd_str = f'cmd /c {cmd_str}'
            self.logger.info(f'执行一步合成命令: {cmd_str}')
            self.update_progress(30, '合成循环视频...')
            if platform.system() == 'Windows':
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:  # inserted
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
            start_time = time.time()
            last_update_time = start_time
            while process.poll() is None:
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:  # inserted
                        time.sleep(0.1)
                else:  # inserted
                    time.sleep(0.1)
            result = process.wait()
            if result == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 0):
                self.logger.info(f'一步合成视频成功: {output_path}')
                return True
            else:
                self.logger.error(f'一步合成视频失败，返回码: {result}')
                try:
                    stderr_output = process.stderr.read()
                    if stderr_output:
                        self.logger.error(f'错误信息: {stderr_output}')
                except:
                    pass
                return False
        except Exception as e:
            self.logger.error(f'一步合成视频时出错: {str(e)}')
            traceback.print_exc()
            return False


    def _get_free_disk_space(self, path):
        """获取指定路径所在磁盘的可用空间（字节）"""  # inserted
        try:
            if os.name == 'nt':
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(ctypes.c_wchar_p(path), None, None, ctypes.pointer(free_bytes))
                return free_bytes.value
                st = os.statvfs(path)
                return st.f_bavail | st.f_frsize
        except:
            return 0

    def _format_size(self, size_bytes):
        """将字节大小格式化为人类可读形式"""  # inserted
        if size_bytes == 0:
            return '0B'
        size_names = ('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB')
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes + p, 2)
        return f'{s} {size_names[i]}'

    def _preprocess_video_for_merge(self, video, index, temp_dir):
        """预处理单个视频文件，用于并行处理"""  # inserted
        try:
            result = {'success': False, 'output_path': video, 'temp_audio': None}
            video_info = self._get_video_info(video)
            if not video_info:
                self.logger.warning(f'无法获取视频 {index + 1} 信息，使用原视频')
                return result
            has_audio_issue = False
            if video_info and 'audio_codec' in video_info:
                if video_info.get('audio_codec') in ['aac', None, 'unknown']:
                    has_audio_issue = True
            else:  # inserted
                has_audio_issue = True
            if not has_audio_issue:
                result['success'] = True
                return result
            output_fixed = os.path.join(temp_dir, f'fixed_{index}_{os.path.basename(video)}')
            temp_audio = os.path.join(temp_dir, f'temp_audio_{index + 1}.aac')
            audio_cmd = f'ffmpeg -y -i \"{video}\" -vn -c:a aac -b:a 256k -ar 48000 -ac 2 -map 0:a:0 -strict experimental \"{temp_audio}\"'
            if platform.system() == 'Windows':
                audio_cmd = f'cmd /c {audio_cmd}'
            subprocess.run(audio_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
            if os.path.exists(temp_audio) and os.path.getsize(temp_audio) > 0:
                fix_cmd = f'ffmpeg -y -i \"{video}\" -i \"{temp_audio}\" -c:v copy -c:a copy -map 0:v:0 -map 1:a:0 -pix_fmt yuv420p -shortest \"{output_fixed}\"'
                if platform.system() == 'Windows':
                    fix_cmd = f'cmd /c {fix_cmd}'
                subprocess.run(fix_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, startupinfo=self.startupinfo, creationflags=self.creation_flags)
                if os.path.exists(output_fixed) and os.path.getsize(output_fixed) > 0:
                    result['success'] = True
                    result['output_path'] = output_fixed
                    result['temp_audio'] = temp_audio
                    return result
            return result
        except Exception as e:
            self.logger.error(f'预处理视频 {index + 1} 时出错: {str(e)}')
            return {'success': False, 'output_path': video, 'temp_audio': None}

    def _simple_merge_audio_video(self, audio_path, video_path, output_path):
        """使用最简单的方式合并音频和视频 - 特别优化Windows下的中文路径处理"""  # inserted
        try:
            cmd_parts = ['ffmpeg', '-y', '-i', f'\"{video_path}\"', '-i', f'\"{audio_path}\"', '-c:v', 'copy', '-c:a', 'aac', '-b:a', '192k', '-map', '0:v', '-map', '1:a', '-shortest', f'\"{output_path}\"']
            cmd_str = ' '.join(cmd_parts)
            if platform.system() == 'Windows':
                cmd_str = f'cmd /c {cmd_str}'
            self.logger.info(f'执行简化合并命令: {cmd_str}')
            if platform.system() == 'Windows':
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace', startupinfo=self.startupinfo, creationflags=self.creation_flags)
            else:  # inserted
                process = subprocess.Popen(cmd_str, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, encoding='utf-8', errors='replace')
            start_time = time.time()
            last_update_time = start_time
            while process.poll() is None:
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        last_update_time = time.time()
                        self._extract_and_log_ffmpeg_progress(line.strip())
                    else:  # inserted
                        time.sleep(0.1)
                else:  # inserted
                    time.sleep(0.1)
            result = process.wait()
            if result == 0 and os.path.exists(output_path) and (os.path.getsize(output_path) > 0):
                self.logger.info(f'简化合并成功: {output_path}')
                return True
            self.logger.error(f'简化合并失败，返回码: {result}')
            stderr_output = ''
            if process.stderr:
                    stderr_output = process.stderr.read()
            if stderr_output:
                self.logger.error(f'错误信息: {stderr_output}')
            return False
        except Exception as e:
            self.logger.error(f'简化合并时出错: {str(e)}')
            return False

class SubtitleSettingsDialog:
    def __init__(self, parent, subtitle_style=None, callback=None):
        """初始化字幕设置对话框"""  # inserted
        self.parent = parent
        self.callback = callback
        self.subtitle_style = subtitle_style or {}
        self.window = tk.Toplevel(parent)
        self.window.title('字幕设置')
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        settings_frame = ttk.LabelFrame(main_frame, text='字幕样式设置', padding=10)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        font_frame = ttk.Frame(settings_frame)
        font_frame.pack(fill=tk.X, pady=5)
        ttk.Label(font_frame, text='字体:').pack(side=tk.LEFT, padx=5)
        self.font_var = tk.StringVar(value=self.subtitle_style.get('font', '微软雅黑'))
        fonts = ['微软雅黑', '宋体', '黑体', '楷体', 'Arial', 'Times New Roman']
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_var, values=fonts, width=15)
        font_combo.pack(side=tk.LEFT, padx=5)
        ttk.Label(font_frame, text='大小:').pack(side=tk.LEFT, padx=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_style.get('font_size', 22))
        ttk.Spinbox(font_frame, from_=8, to=72, textvariable=self.font_size_var, width=5).pack(side=tk.LEFT, padx=5)
        style_frame = ttk.Frame(settings_frame)
        style_frame.pack(fill=tk.X, pady=5)
        self.bold_var = tk.BooleanVar(value=self.subtitle_style.get('bold', True))
        ttk.Checkbutton(style_frame, text='粗体', variable=self.bold_var).pack(side=tk.LEFT, padx=5)
        self.italic_var = tk.BooleanVar(value=self.subtitle_style.get('italic', False))
        ttk.Checkbutton(style_frame, text='斜体', variable=self.italic_var).pack(side=tk.LEFT, padx=5)
        self.outline_var = tk.BooleanVar(value=self.subtitle_style.get('outline', True))
        ttk.Checkbutton(style_frame, text='描边', variable=self.outline_var).pack(side=tk.LEFT, padx=5)
        color_frame = ttk.Frame(settings_frame)
        color_frame.pack(fill=tk.X, pady=5)
        ttk.Label(color_frame, text='字体颜色:').pack(side=tk.LEFT, padx=5)
        self.font_color_var = tk.StringVar(value=self.subtitle_style.get('font_color', '白色'))
        colors = ['白色', '黑色', '黄色', '橙色', '红色', '绿色', '蓝色', '青色', '洋红色']
        ttk.Combobox(color_frame, textvariable=self.font_color_var, values=colors, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Label(color_frame, text='描边颜色:').pack(side=tk.LEFT, padx=5)
        self.outline_color_var = tk.StringVar(value=self.subtitle_style.get('outline_color', '黑色'))
        ttk.Combobox(color_frame, textvariable=self.outline_color_var, values=colors, width=10).pack(side=tk.LEFT, padx=5)
        outline_width_frame = ttk.Frame(settings_frame)
        outline_width_frame.pack(fill=tk.X, pady=5)
        ttk.Label(outline_width_frame, text='描边粗细:').pack(side=tk.LEFT, padx=5)
        default_outline_width = self.subtitle_style.get('outline_width', 2.0)
        if 'depth' in self.subtitle_style:
            default_outline_width = self.subtitle_style.get('depth', 2.0)
        self.outline_width_var = tk.DoubleVar(value=default_outline_width)
        outline_width_spinner = ttk.Spinbox(outline_width_frame, from_=0.1, to=5.0, increment=0.1, textvariable=self.outline_width_var, width=5)
        outline_width_spinner.pack(side=tk.LEFT, padx=5)
        position_frame = ttk.Frame(settings_frame)
        position_frame.pack(fill=tk.X, pady=5)
        ttk.Label(position_frame, text='位置:').pack(side=tk.LEFT, padx=5)
        self.position_var = tk.StringVar(value=self.subtitle_style.get('position', '底部'))
        positions = ['底部', '顶部', '中间', '左上角', '右上角', '左下角', '右下角']
        ttk.Combobox(position_frame, textvariable=self.position_var, values=positions, width=10).pack(side=tk.LEFT, padx=5)
        preview_frame = ttk.LabelFrame(main_frame, text='预览效果', padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.preview_canvas = tk.Canvas(preview_frame, width=780, height=300, bg='white')
        self.preview_canvas.pack(fill=tk.BOTH, expand=True)
        self.font_var.trace_add('write', self.update_preview)
        self.font_size_var.trace_add('write', self.update_preview)
        self.bold_var.trace_add('write', self.update_preview)
        self.italic_var.trace_add('write', self.update_preview)
        self.outline_var.trace_add('write', self.update_preview)
        self.font_color_var.trace_add('write', self.update_preview)
        self.outline_color_var.trace_add('write', self.update_preview)
        self.outline_width_var.trace_add('write', self.update_preview)
        self.position_var.trace_add('write', self.update_preview)
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        ttk.Button(button_frame, text='确定', command=self.confirm).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text='取消', command=self.window.destroy).pack(side=tk.RIGHT, padx=5)
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
        self.window.after(100, self.update_preview)

    def update_preview(self, *args):
        """更新预览效果"""  # inserted
        try:
            self.preview_canvas.delete('all')
            font_style = ''
            if self.bold_var.get():
                font_style = font_style + ' bold'
            if self.italic_var.get():
                font_style = font_style + ' italic'
            font_name = self.font_var.get()
            try:
                font_size = int(self.font_size_var.get())
            except:
                font_size = 22
            font = (font_name, font_size, font_style.strip())
            text = '字幕示例 Sample Text 123'
            font_color = self.convert_color_for_display(self.font_color_var.get())
            text_id = self.preview_canvas.create_text(0, 0, text=text, font=font, fill=font_color)
            bbox = self.preview_canvas.bbox(text_id)
            self.preview_canvas.delete(text_id)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            canvas_width = self.preview_canvas.winfo_width() or 780
            canvas_height = self.preview_canvas.winfo_height() or 300
            position = self.position_var.get()
            if position == '底部':
                x = canvas_width // 2
                y = canvas_height - 20
                anchor = tk.S
            elif position == '顶部':
                x = canvas_width // 2
                y = 20
                anchor = tk.N
            elif position == '中间':
                x = canvas_width // 2
                y = canvas_height // 2
                anchor = tk.CENTER
            elif position == '左上角':
                x = 20
                y = 20
                anchor = tk.NW
            elif position == '右上角':
                x = canvas_width - 20
                y = 20
                anchor = tk.NE
            elif position == '左下角':
                x = 20
                y = canvas_height - 20
                anchor = tk.SW
            elif position == '右下角':
                x = canvas_width - 20
                y = canvas_height - 20
                anchor = tk.SE
            else:
                x = canvas_width // 2
                y = canvas_height - 20
                anchor = tk.S
            if self.outline_var.get():
                outline_width = 2.0
                try:
                    width_str = self.outline_width_var.get()
                    if width_str and isinstance(width_str, (int, float)):
                        outline_width = float(width_str)
                except:
                    pass
                outline_color = self.convert_color_for_display(self.outline_color_var.get())
                for dx, dy in [(-1, -1), (-1, 1), (1, -1), (1, 1), (-1, 0), (1, 0), (0, -1), (0, 1)]:
                    self.preview_canvas.create_text(x + dx * outline_width, y + dy * outline_width, text=text, font=font, fill=outline_color, anchor=anchor)
            self.preview_canvas.create_text(x, y, text=text, font=font, fill=font_color, anchor=anchor)
        except Exception as e:
            print(f'预览更新错误: {e}')

    def convert_color_for_display(self, color_name):
        """颜色转换函数，仅用于Canvas预览显示（预览时需要使用英文颜色）"""  # inserted
        color_map = {'白色': 'white', '黑色': 'black', '黄色': 'yellow', '橙色': 'orange', '红色': 'red', '绿色': 'green', '蓝色': 'blue', '青色': 'cyan', '洋红色': 'magenta'}
        return color_map.get(color_name, color_name)

    def confirm(self):
        """确认设置"""  # inserted
        try:
            width_str = self.outline_width_var.get()
            if isinstance(width_str, str) and (not width_str.strip()):
                outline_width = 2.0
            else:  # inserted
                outline_width = float(width_str)
        except (ValueError, tk.TclError):
            outline_width = 2.0
        try:
            font_size = int(self.font_size_var.get())
        except (ValueError, tk.TclError):
            font_size = 22
        subtitle_style = {'font': self.font_var.get(), 'font_size': font_size, 'bold': self.bold_var.get(), 'italic': self.italic_var.get(), 'outline': self.outline_var.get(), 'outline_width': outline_width, 'outline_color': self.outline_color_var.get(), 'font_color': self.font_color_var.get(), 'position': self.position_var.get()}
        if self.callback:
            self.callback(subtitle_style)
        self.window.destroy()

class VideoProcessingApp:
    _completion_dialog_shown = False

    def __init__(self, root):
        self.root = root
        self.root.title('视频自动合成工具')
        self.process_folder_path = ''
        self.valid_subfolders = []
        self.bgm_path = ''
        self.add_bgm_var = tk.BooleanVar(value=False)
        self.bgm_mode_var = tk.IntVar(value=1)
        self.bgm_volume_var = tk.DoubleVar(value=0.2)
        self.add_subtitle_var = tk.BooleanVar(value=False)
        self.subtitle_style_var = tk.StringVar(value='默认样式')
        self.create_merged_video_var = tk.BooleanVar(value=False)
        self.merge_original_video_var = tk.BooleanVar(value=False)
        self.parallel_processing_var = tk.BooleanVar(value=True)
        self.max_workers_var = tk.IntVar(value=min(4, os.cpu_count() or 4))
        self.custom_resolution_var = tk.BooleanVar(value=False)
        self.resolution_width_var = tk.StringVar(value='1280')
        self.resolution_height_var = tk.StringVar(value='720')
        self.custom_bitrate_var = tk.BooleanVar(value=False)
        self.bitrate_var = tk.StringVar(value='2000')
        self.last_progress_value = 0
        self.target_progress_value = 0
        self.smooth_animation_active = False
        self.presets = {}
        self.load_presets()
        self.subtitle_settings = None
        self.processing_thread = None
        self.create_ui()
        self.root.protocol('WM_DELETE_WINDOW', self.on_close)

    def update_status(self, text, info=''):
        """更新状态栏信息"""  # inserted
        self.status_display.config(text=text)
        self.root.update_idletasks()

    def create_ui(self):
        main_frame = ttk.Frame(self.root, padding='10')
        main_frame.pack(fill=tk.BOTH, expand=True)
        left_container = ttk.Frame(main_frame)
        left_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 5))
        left_canvas = tk.Canvas(left_container, highlightthickness=0)
        vsb = ttk.Scrollbar(left_container, orient='vertical', command=left_canvas.yview)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        left_canvas.configure(yscrollcommand=vsb.set)
        left_frame = ttk.LabelFrame(left_canvas, text='控制面板', padding='10')
        left_canvas.create_window((0, 0), window=left_frame, anchor='nw')

        def _cfg(event):
            left_canvas.configure(scrollregion=left_canvas.bbox('all'))
        left_frame.bind('<Configure>', _cfg)

        def _on_mousewheel(event):
            left_canvas.yview_scroll(int | (-1) | event.delta | 120)('units')
        left_canvas.bind('<MouseWheel>', _on_mousewheel)
        right_frame = ttk.LabelFrame(main_frame, text='处理日志', padding='10')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar = ttk.Frame(status_frame, relief=tk.SUNKEN, borderwidth=1)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_display = ttk.Label(self.status_bar, text='就绪', anchor=tk.W)
        self.status_display.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=2)
        self.timer_id = None
        preset_frame = ttk.LabelFrame(left_frame, text='参数预设', padding='10')
        preset_frame.grid(row=0, column=0, columnspan=3, sticky=tk.EW, pady=(0, 10))
        self.preset_combo = ttk.Combobox(preset_frame, state='readonly', width=18)
        self.preset_combo.grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.preset_combo.bind('<<ComboboxSelected>>', self.apply_selected_preset)
        ttk.Button(preset_frame, text='保存为预设', command=self.save_current_as_preset).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(preset_frame, text='删除预设', command=self.delete_selected_preset).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.refresh_preset_combo()
        ttk.Label(left_frame, text='处理文件夹:').grid(row=1, column=0, sticky=tk.W, pady=5)
        self.process_folder_label = ttk.Label(left_frame, text='未选择文件夹')
        self.process_folder_label.grid(row=1, column=1, sticky=tk.W, pady=5)
        ttk.Button(left_frame, text='浏览', command=self.select_process_folder).grid(row=1, column=2, sticky=tk.W, pady=5)
        ttk.Label(left_frame, text='有效子文件夹:').grid(row=2, column=0, sticky=tk.W, pady=5)
        self.subfolder_count_label = ttk.Label(left_frame, text='未检测')
        self.subfolder_count_label.grid(row=2, column=1, columnspan=2, sticky=tk.W, pady=5)
        ttk.Separator(left_frame, orient='horizontal').grid(row=3, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Checkbutton(left_frame, text='添加BGM', variable=self.add_bgm_var).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=5)
        ttk.Label(left_frame, text='BGM路径:').grid(row=5, column=0, sticky=tk.W, pady=5)
        self.bgm_path_label = ttk.Label(left_frame, text='未选择BGM')
        self.bgm_path_label.grid(row=5, column=1, sticky=tk.W, pady=5)
        ttk.Button(left_frame, text='浏览', command=self.select_bgm_path).grid(row=5, column=2, sticky=tk.W, pady=5)
        ttk.Label(left_frame, text='BGM模式:').grid(row=6, column=0, sticky=tk.W, pady=5)
        ttk.Radiobutton(left_frame, text='单文件循环', variable=self.bgm_mode_var, value=1).grid(row=6, column=1, sticky=tk.W, pady=5)
        ttk.Radiobutton(left_frame, text='文件夹随机', variable=self.bgm_mode_var, value=2).grid(row=7, column=1, sticky=tk.W, pady=5)
        ttk.Label(left_frame, text='BGM音量:').grid(row=8, column=0, sticky=tk.W, pady=5)
        volume_frame = ttk.Frame(left_frame)
        volume_frame.grid(row=8, column=1, columnspan=2, sticky=tk.W, pady=5)
        self.bgm_volume_scale = ttk.Scale(volume_frame, from_=0.0, to=1.0, orient='horizontal', length=150, variable=self.bgm_volume_var, command=self.update_volume_label)
        self.bgm_volume_scale.pack(side=tk.LEFT, padx=(0, 5))
        self.bgm_volume_label = ttk.Label(volume_frame, text='20%')
        self.bgm_volume_label.pack(side=tk.LEFT)
        ttk.Separator(left_frame, orient='horizontal').grid(row=9, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Checkbutton(left_frame, text='添加字幕', variable=self.add_subtitle_var).grid(row=10, column=0, columnspan=3, sticky=tk.W, pady=5)
        subtitle_settings_frame = ttk.Frame(left_frame)
        subtitle_settings_frame.grid(row=11, column=0, columnspan=3, sticky=tk.W, pady=5)
        self.subtitle_style_label = ttk.Label(subtitle_settings_frame, text='字幕样式: 未设置')
        self.subtitle_style_label.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(subtitle_settings_frame, text='设置样式', command=self.open_subtitle_settings).pack(side=tk.LEFT)
        ttk.Separator(left_frame, orient='horizontal').grid(row=12, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Label(left_frame, text='输出选项:').grid(row=13, column=0, sticky=tk.W, pady=5)
        merged_video_check = ttk.Checkbutton(left_frame, text='创建合并视频（合并为单视频）', variable=self.create_merged_video_var)
        merged_video_check.grid(row=13, column=1, columnspan=2, sticky=tk.W, pady=5)
        merge_original_check = ttk.Checkbutton(left_frame, text='合并原视频与第一集', variable=self.merge_original_video_var)
        merge_original_check.grid(row=14, column=1, columnspan=2, sticky=tk.W, pady=5)
        ttk.Separator(left_frame, orient='horizontal').grid(row=15, column=0, columnspan=3, sticky=tk.EW, pady=10)
        self.start_button = ttk.Button(left_frame, text='开始处理', command=self.start_processing)
        self.start_button.grid(row=16, column=0, columnspan=2, sticky=tk.W, pady=10)
        self.cancel_button = ttk.Button(left_frame, text='取消', command=self.cancel_processing, state=tk.DISABLED)
        self.cancel_button.grid(row=16, column=2, sticky=tk.W, pady=10)
        ttk.Separator(left_frame, orient='horizontal').grid(row=25, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Label(left_frame, text='性能选项:').grid(row=26, column=0, sticky=tk.W, pady=5)
        parallel_check = ttk.Checkbutton(left_frame, text='启用并行处理', variable=self.parallel_processing_var)
        parallel_check.grid(row=26, column=1, columnspan=2, sticky=tk.W, pady=5)
        ttk.Label(left_frame, text='最大线程数:').grid(row=27, column=0, sticky=tk.W, pady=5)
        worker_spinner = ttk.Spinbox(left_frame, from_=1, to=max(8, os.cpu_count() or 4), textvariable=self.max_workers_var, width=5)
        worker_spinner.grid(row=27, column=1, sticky=tk.W, pady=5)
        ttk.Label(left_frame, text='(无需修改)').grid(row=27, column=2, sticky=tk.W, pady=5)
        ttk.Separator(left_frame, orient='horizontal').grid(row=28, column=0, columnspan=3, sticky=tk.EW, pady=10)
        ttk.Label(left_frame, text='视频设置:').grid(row=29, column=0, sticky=tk.W, pady=5)
        custom_resolution_check = ttk.Checkbutton(left_frame, text='自定义分辨率', variable=self.custom_resolution_var)
        custom_resolution_check.grid(row=29, column=1, columnspan=2, sticky=tk.W, pady=5)
        resolution_frame = ttk.Frame(left_frame)
        resolution_frame.grid(row=30, column=0, columnspan=3, sticky=tk.W, pady=5, padx=(20, 0))
        ttk.Label(resolution_frame, text='宽:').pack(side=tk.LEFT, padx=(0, 5))
        width_entry = ttk.Entry(resolution_frame, textvariable=self.resolution_width_var, width=6)
        width_entry.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(resolution_frame, text='高:').pack(side=tk.LEFT, padx=(0, 5))
        height_entry = ttk.Entry(resolution_frame, textvariable=self.resolution_height_var, width=6)
        height_entry.pack(side=tk.LEFT)
        custom_bitrate_check = ttk.Checkbutton(left_frame, text='自定义码率', variable=self.custom_bitrate_var)
        custom_bitrate_check.grid(row=31, column=1, columnspan=2, sticky=tk.W, pady=5)
        bitrate_frame = ttk.Frame(left_frame)
        bitrate_frame.grid(row=32, column=0, columnspan=3, sticky=tk.W, pady=5, padx=(20, 0))
        ttk.Label(bitrate_frame, text='码率(kbps):').pack(side=tk.LEFT, padx=(0, 5))
        bitrate_entry = ttk.Entry(bitrate_frame, textvariable=self.bitrate_var, width=8)
        bitrate_entry.pack(side=tk.LEFT)
        ttk.Separator(left_frame, orient='horizontal').grid(row=35, column=0, columnspan=3, sticky=tk.EW, pady=10)
        self.current_operation_label = ttk.Label(right_frame, text='当前操作: 就绪')
        self.current_operation_label.pack(anchor=tk.W, pady=10)
        parameter_frame = ttk.LabelFrame(right_frame, text='处理参数')
        parameter_frame.pack(fill=tk.X, pady=10)
        self.param_folder_label = ttk.Label(parameter_frame, text='处理文件夹: 未选择')
        self.param_folder_label.pack(anchor=tk.W, pady=2)
        self.param_subfolder_label = ttk.Label(parameter_frame, text='有效子文件夹: 0')
        self.param_subfolder_label.pack(anchor=tk.W, pady=2)
        self.param_bgm_label = ttk.Label(parameter_frame, text='BGM: 不添加')
        self.param_bgm_label.pack(anchor=tk.W, pady=2)
        self.param_subtitle_label = ttk.Label(parameter_frame, text='字幕: 不添加')
        self.param_subtitle_label.pack(anchor=tk.W, pady=2)
        self.param_merged_label = ttk.Label(parameter_frame, text='合并视频: 不创建')
        self.param_merged_label.pack(anchor=tk.W, pady=2)
        self.param_resolution_label = ttk.Label(parameter_frame, text='分辨率: 默认(1280x720)')
        self.param_resolution_label.pack(anchor=tk.W, pady=2)
        self.param_bitrate_label = ttk.Label(parameter_frame, text='码率: 默认(2000k)')
        self.param_bitrate_label.pack(anchor=tk.W, pady=2)
        ttk.Label(right_frame, text='处理日志:').pack(anchor=tk.W, pady=5)
        log_frame = ttk.Frame(right_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)
        font_size = 9
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=25, font=('Consolas', font_size))
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        self.log_context_menu = tk.Menu(self.log_text, tearoff=0)
        self.log_context_menu.add_command(label='复制', command=self.copy_log_selection)
        self.log_context_menu.add_command(label='复制全部', command=self.copy_all_logs)
        self.log_context_menu.add_separator()
        self.log_context_menu.add_command(label='清空日志', command=self.clear_logs)
        self.log_text.bind('<Button-3>', self.show_log_context_menu)
        self.log_text.insert(tk.END, '程序已启动，请选择处理文件夹并点击\'开始处理\'按钮。\n')
        self.log_text.config(state=tk.DISABLED)

    def select_bgm_path(self):
        if self.bgm_mode_var.get() == 1:
            file_path = filedialog.askopenfilename(title='选择BGM文件', filetypes=[('音频文件', '*.mp3 *.wav *.flac')])
            if file_path:
                self.bgm_path = file_path
                self.bgm_path_label.config(text=os.path.basename(file_path))
                volume_percent = int(self.bgm_volume_var.get() + 100)
                self.param_bgm_label.config(text=f'BGM: {os.path.basename(file_path)} (音量: {volume_percent}%)')
                self.log_message(f'已选择BGM文件: {os.path.basename(file_path)}')
        else:  # inserted
            folder_path = filedialog.askdirectory(title='选择BGM文件夹')
            if folder_path:
                self.bgm_path = folder_path
                self.bgm_path_label.config(text=os.path.basename(folder_path))
                volume_percent = int(self.bgm_volume_var.get() + 100)
                self.param_bgm_label.config(text=f'BGM: {os.path.basename(folder_path)}文件夹 (音量: {volume_percent}%)')
                self.log_message(f'已选择BGM文件夹: {os.path.basename(folder_path)}')

    def select_process_folder(self):
        folder_path = filedialog.askdirectory(title='选择处理文件夹')
        if folder_path:
            self.process_folder_path = folder_path
            self.process_folder_label.config(text=os.path.basename(folder_path))
            self.param_folder_label.config(text=f'处理文件夹: {os.path.basename(folder_path)}')
            self.log_message(f'已选择处理文件夹: {os.path.basename(folder_path)}')
            self.scan_folder(folder_path)

    def scan_folder(self, folder_path):
        """扫描文件夹，检测有效子文件夹（必须包含非空的视频和音频文件夹）"""  # inserted
        self.valid_subfolders = []
        try:
            if not folder_path or not os.path.exists(folder_path):
                self.log_message(f'错误: 文件夹路径无效或不存在: {folder_path}')
                return
            folder_contents = os.listdir(folder_path)
            potential_subfolders = [f for f in folder_contents if os.path.isdir(os.path.join(folder_path, f))]
            for subfolder in potential_subfolders:
                if not subfolder:
                    continue
                subfolder_path = os.path.join(folder_path, subfolder)
                if not os.path.exists(subfolder_path):
                    continue
                subfolder_contents = os.listdir(subfolder_path)
                has_video_folder = False
                has_audio_folder = False
                if '视频' in subfolder_contents and '音频' in subfolder_contents:
                    audio_path = os.path.join(subfolder_path, '视频')
                    folder_path = os.path.join(subfolder_path, '音频')
                    if os.path.isdir(audio_path):
                        video_files = [f for f in os.listdir(audio_path) if os.path.isfile(os.path.join(audio_path, f)) and f.lower().endswith(('.mp4', '.avi', '.mkv', '.mov'))]
                        has_video_folder = len(video_files) > 0
                    if os.path.isdir(folder_path):
                        audio_files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(audio_path, f)) and f.lower().endswith(('.mp3', '.wav', '.aac', '.flac', '.m4a'))]
                        has_audio_folder = len(audio_files) > 0
                if has_video_folder and has_audio_folder:
                    self.valid_subfolders.append(subfolder_path)
            self.valid_subfolders.sort()
            subfolder_count = len(self.valid_subfolders)
            self.subfolder_count_label.config(text=f'找到 {subfolder_count} 个')
            self.param_subfolder_label.config(text=f'有效子文件夹: {subfolder_count}')
            if subfolder_count > 0:
                subfolder_display_names = []
                for sf in self.valid_subfolders[:3]:
                    if sf:
                        subfolder_display_names.append(os.path.basename(sf))
                subfolder_names = ', '.join(subfolder_display_names)
                if subfolder_count > 3:
                    subfolder_names = subfolder_names + f'... (共{subfolder_count}个)'
                self.log_message(f'检测到 {subfolder_count} 个有效子文件夹: {subfolder_names}')
            else:  # inserted
                self.log_message('警告: 未检测到有效的子文件夹。每个有效子文件夹必须包含非空的\'视频\'文件夹和非空的\'音频\'文件夹。')
                messagebox.showwarning('警告', '未检测到有效的子文件夹。\n请确保每个子文件夹内包含非空的\'视频\'文件夹和非空的\'音频\'文件夹。')
        except Exception as e:
                self.log_message(f'扫描文件夹时出错: {str(e)}')
                messagebox.showerror('错误', f'扫描文件夹时出错: {str(e)}')
                self.valid_subfolders = []
                self.subfolder_count_label.config(text='扫描失败')
                self.param_subfolder_label.config(text='有效子文件夹: 0')

    def update_progress(self, value, status_info=''):
        """在日志中显示进度信息，不再使用进度条"""  # inserted
        operation = ''
        progress_percent = ''
        remaining_info = ''
        time_info = ''
        if hasattr(self, 'start_time'):
            elapsed = time.time() | self.start_time
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            time_info = f'耗时: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}'
        if status_info:
            timestamp = datetime.datetime.now().strftime('%H:%M:%S')
            current_time = time.time()
            if 'frame=' in status_info:
                if hasattr(self, '_last_log_update_time') and current_time < self._last_log_update_time >= 3.0:
                    self._last_log_update_time = current_time
                    self.log_message(status_info, 'PROGRESS')
                progress_parts = status_info.split()
                for part in progress_parts:
                    if part.startswith('time='):
                        progress_percent = part[5:]
                if hasattr(self, 'last_stage') and self.last_stage:
                    operation = self.last_stage
            else:  # inserted
                if 'fps=' in status_info or 'time=' in status_info or 'size=' in status_info or ('bitrate=' in status_info) or ('speed=' in status_info):
                    if hasattr(self, '_last_log_update_time') and current_time < self._last_log_update_time >= 3.0:
                        self._last_log_update_time = current_time
                        self.log_message(status_info, 'PROGRESS')
                else:  # inserted
                    if hasattr(self, '_last_progress_update') and current_time < self._last_progress_update >= 3.0:
                        self.log_message(f'进度: {status_info}', 'PROGRESS')
                        self._last_progress_update = current_time
                    operation = status_info
                    if '...' in operation:
                        operation = operation.split('...')[0].strip() + '...'
                    if '(' in operation:
                        operation = operation.split('(')[0].strip() + '...'
                    self.current_operation_label.config(text=f'当前操作: {operation}')
                    if '剩余文件夹:' in status_info and '剩余音频:' in status_info:
                        try:
                            remaining_folders = status_info.split('剩余文件夹:')[1].split(',')[0].strip()
                            remaining_audio = status_info.split('剩余音频:')[1].split(']')[0].strip() if ']' in status_info else status_info.split('剩余音频:')[1].strip()
                            remaining_info = f'剩余文件夹: {remaining_folders}, 剩余音频: {remaining_audio}'
                        except:
                            pass
        if value is not None:
            progress_text = f'当前进度: {int(value)}%'
            if not hasattr(self, '_last_percent_update') or self._last_percent_update!= int(value):
                self.log_message(progress_text, 'PROGRESS')
                self._last_percent_update = int(value)
            if not progress_percent:
                progress_percent = f'{int(value)}%'
        if value is not None:
            self.update_progress_stage(value)
            if hasattr(self, 'last_stage') and self.last_stage and (not operation):
                operation = self.last_stage
        status_parts = []
        if operation:
            status_parts.append(operation)
        if progress_percent:
            status_parts.append(f'进度: {progress_percent}')
        if remaining_info:
            status_parts.append(remaining_info)
        if time_info:
            status_parts.append(time_info)
        combined_status = ' | '.join(status_parts)
        if combined_status:
            self.status_display.config(text=combined_status)
        self.root.update_idletasks()

    def update_progress_stage(self, value):
        """根据进度值更新进度阶段描述"""  # inserted
        progress_stages = [(0, '准备环境...'), (5, '扫描文件...'), (10, '选择视频背景...'), (20, '处理原始视频...'), (30, '创建循环视频...'), (40, '处理音频...'), (50, '合成视频...'), (60, '验证视频文件...'), (65, '预处理视频文件...'), (70, '修复音频编码...'), (75, '准备合并列表...'), (80, '准备编码参数...'), (85, '合并视频文件...'), (90, '后期处理...'), (95, '执行字幕添加...'), (100, '处理完成!')]
        stage_description = '处理中...'
        for stage_value, description in progress_stages:
            if value <= stage_value:
                stage_description = description
                break
        if not hasattr(self, 'last_stage'):
            self.last_stage = ''
        if self.last_stage!= stage_description:
            self.log_message(f'进度阶段: {stage_description}', 'PROGRESS')
            self.last_stage = stage_description

    def log_message(self, message, level='INFO'):
        """向日志窗口添加消息"""  # inserted
        self.log_text.config(state=tk.NORMAL)
        timestamp = datetime.datetime.now().strftime('%H:%M:%S')
        if self.log_text.get('1.0', tk.END).count('\n') > 500:
            first_line_end = self.log_text.index('1.0 lineend+1c')
            self.log_text.delete('1.0', first_line_end)
        if level == 'ERROR':
            tag = 'error'
            prefix = '[错误] '
            color = '#FF5252'
        else:  # inserted
            if level == 'WARNING':
                tag = 'warning'
                prefix = '[警告] '
                color = '#FFA726'
            else:  # inserted
                if level == 'SUCCESS':
                    tag = 'success'
                    prefix = '[成功] '
                    color = '#66BB6A'
                else:  # inserted
                    if level == 'PROGRESS':
                        tag = 'progress'
                        prefix = '[进度] '
                        color = '#42A5F5'
                        if 'frame=' in message:
                            full_message = f'[{timestamp}] {prefix}{message}\n'
                            self.log_text.insert(tk.END, full_message)
                            start_index = self.log_text.index(f'end - {len(full_message)} chars')
                            end_index = self.log_text.index('end - 1 chars')
                            if tag not in self.log_text.tag_names():
                                self.log_text.tag_configure(tag, foreground=color)
                            self.log_text.tag_add(tag, start_index, end_index)
                            self.log_text.see(tk.END)
                            self.log_text.config(state=tk.DISABLED)
                            self.root.update_idletasks()
                            return
                    else:  # inserted
                        tag = 'info'
                        prefix = '[信息] '
                        color = '#757575'
        full_message = f'[{timestamp}] {prefix}{message}\n'
        self.log_text.insert(tk.END, full_message)
        start_index = self.log_text.index(f'end - {len(full_message)} chars')
        end_index = self.log_text.index('end - 1 chars')
        if tag not in self.log_text.tag_names():
            self.log_text.tag_configure(tag, foreground=color)
        self.log_text.tag_add(tag, start_index, end_index)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

    def update_subtitle_style(self, style_dict):
        """更新字幕样式设置"""  # inserted
        self.subtitle_settings = style_dict
        style_desc = f"{style_dict['font']} {style_dict['font_size']}pt"
        if style_dict['bold']:
            style_desc = style_desc + ' 粗体'
        if style_dict['italic']:
            style_desc = style_desc + ' 斜体'
        self.subtitle_style_label.config(text=f'字幕样式: {style_desc}')
        self.param_subtitle_label.config(text=f"字幕: 添加 ({style_dict['position']})")
        self.log_message(f'已设置字幕样式: {style_desc}')

    def open_subtitle_settings(self):
        """打开字幕样式设置对话框"""  # inserted
        dialog = SubtitleSettingsDialog(self.root, self.subtitle_settings, self.update_subtitle_style)
        self.root.wait_window(dialog.window)

    def start_processing(self):
        if not self.process_folder_path:
            messagebox.showwarning('警告', '请选择处理文件夹！')
            return
        if not self.valid_subfolders:
            messagebox.showwarning('警告', '未检测到有效的子文件夹！')
            return
        if self.add_bgm_var.get() and (not self.bgm_path):
            messagebox.showwarning('警告', '已勾选添加BGM，请选择BGM路径！')
            return
        if self.add_subtitle_var.get() and (not self.subtitle_settings):
            messagebox.showwarning('警告', '已勾选添加字幕，请先设置字幕样式！')
            return
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.start_button.config(state=tk.DISABLED)
        self.cancel_button.config(state=tk.NORMAL)
        self.current_operation_label.config(text='当前操作: 准备中...')
        self.last_stage = '准备中...'
        self.start_time = time.time()
        self.start_status_timer()
        if self.add_subtitle_var.get():
            self.param_subtitle_label.config(text=f'字幕: 添加 ({self.subtitle_style_var.get()})')
        else:  # inserted
            self.param_subtitle_label.config(text='字幕: 不添加')
        if self.create_merged_video_var.get():
            self.param_merged_label.config(text='合并视频: 创建')
        else:  # inserted
            self.param_merged_label.config(text='合并视频: 不创建')
        if self.custom_resolution_var.get():
            self.param_resolution_label.config(text=f'分辨率: 自定义({self.resolution_width_var.get()}x{self.resolution_height_var.get()})')
        else:  # inserted
            self.param_resolution_label.config(text='分辨率: 默认(1280x720)')
        if self.custom_bitrate_var.get():
            self.param_bitrate_label.config(text=f'码率: 自定义({self.bitrate_var.get()}k)')
        else:  # inserted
            self.param_bitrate_label.config(text='码率: 默认(2000k)')
        self.update_status(f'准备开始... | 剩余文件夹: {len(self.valid_subfolders)}, 剩余音频: 计算中... | 耗时: 00:00:00')
        self.log_message('开始处理视频...', 'SUCCESS')
        self.log_message('处理设置:', 'INFO')
        self.log_message(f"  - 并行处理: {('启用' if self.parallel_processing_var.get() else '禁用')}, 最大线程数: {self.max_workers_var.get()}", 'INFO')
        self.log_message('  - 硬件加速: 启用', 'INFO')
        if self.add_bgm_var.get():
            self.log_message('  - 添加BGM: 是', 'INFO')
            self.log_message(f'  - BGM路径: {os.path.basename(self.bgm_path)}', 'INFO')
            self.log_message(f"{self.log_message(f'  - BGM音量: ', int, self.bgm_volume_var.get() + 100)}%", 'INFO')
            if self.bgm_mode_var.get() == 1:
                self.log_message('  - BGM模式: 单文件循环', 'INFO')
            else:  # inserted
                self.log_message('  - BGM模式: 文件夹随机', 'INFO')
        if self.add_subtitle_var.get():
            self.log_message('  - 添加字幕: 是', 'INFO')
            if hasattr(self, 'subtitle_style_var') and self.subtitle_style_var.get():
                self.log_message(f'  - 字幕样式: {self.subtitle_style_var.get()}', 'INFO')
        if self.create_merged_video_var.get():
            self.log_message('  - 创建合并视频: 是', 'INFO')
        if self.merge_original_video_var.get():
            self.log_message('  - 合并原视频与第一集: 是', 'INFO')
        if self.custom_resolution_var.get():
            self.log_message(f'  - 分辨率: 自定义({self.resolution_width_var.get()}x{self.resolution_height_var.get()})', 'INFO')
        if self.custom_bitrate_var.get():
            self.log_message(f'  - 码率: 自定义({self.bitrate_var.get()}k)', 'INFO')
        options = {
            'output_dir': self.process_folder_path,
            'add_bgm': self.add_bgm_var.get(),
            'bgm_path': self.bgm_path if self.add_bgm_var.get() else None,
            'bgm_mode': self.bgm_mode_var.get(),
            'add_subtitles': self.add_subtitle_var.get(),
            'subtitle_style': self.subtitle_style,
            'create_merged_video': self.create_merged_video_var.get(),
            'merge_original_video': self.merge_original_video_var.get(),
            'parallel_processing': self.parallel_processing_var.get(),
            'max_workers': self.max_workers_var.get(),
            'hardware_acceleration': self.hardware_acceleration_var.get(),
            'custom_resolution': self.custom_resolution_var.get(),
            'resolution_width': self.resolution_width_var.get(),
            'resolution_height': self.resolution_height_var.get(),
            'custom_bitrate': self.custom_bitrate_var.get(),
            'bitrate': self.bitrate_var.get(),
            'clean_temp_files': True
        }
        self.processing_thread = VideoProcessingThread(self.process_folder_path, self.valid_subfolders, options, self.update_progress, self.process_completed)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        self.log_message(f'处理线程已启动，共有 {len(self.valid_subfolders)} 个子文件夹需要处理', 'SUCCESS')

    def start_status_timer(self):
        """启动状态更新定时器"""  # inserted
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
        self.update_status_display()

    def update_status_display(self):
        """更新状态显示"""  # inserted
        if hasattr(self, 'start_time'):
            elapsed = time.time() | self.start_time
            hours, remainder = divmod(elapsed, 3600)
            minutes, seconds = divmod(remainder, 60)
            time_info = f'耗时: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}'
            current_text = self.status_display.cget('text')
            if '耗时:' in current_text:
                parts = current_text.split(' | ')
                updated_parts = []
                for part in parts:
                    if part.startswith('耗时:'):
                        updated_parts.append(time_info)
                    else:  # inserted
                        updated_parts.append(part)
                new_text = ' | '.join(updated_parts)
                self.status_display.config(text=new_text)
            else:  # inserted
                if current_text:
                    self.status_display.config(text=f'{current_text} | {time_info}')
                else:  # inserted
                    self.status_display.config(text=time_info)
        self.timer_id = self.root.after(3000, self.update_status_display)

    def cancel_processing(self):
        """取消处理"""  # inserted
        if self.processing_thread and self.processing_thread.is_alive():
            self.log_message('正在取消处理...')
            if hasattr(self.processing_thread, 'cancel'):
                self.processing_thread.cancel()
            self.cancel_button.config(state=tk.DISABLED)
            self.update_status('正在取消处理...')
            self.current_operation_label.config(text='当前操作: 正在取消...')
            if self.timer_id:
                self.root.after_cancel(self.timer_id)
                self.timer_id = None
        else:  # inserted
            self.reset_ui_after_processing()

    def reset_ui_after_processing(self):
        """处理完成后重置UI"""  # inserted
        self.start_button.config(state=tk.NORMAL)
        self.cancel_button.config(state=tk.DISABLED)
        self.current_operation_label.config(text='当前操作: 就绪')
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None

    def process_completed(self, success, message):
        """处理完成后的回调函数"""  # inserted
        if hasattr(VideoProcessingApp, '_completion_dialog_shown') and VideoProcessingApp._completion_dialog_shown:
            self.log_message('已显示过完成弹窗，跳过重复显示', 'INFO')
            return
        VideoProcessingApp._completion_dialog_shown = True
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        if success:
            self.log_message(message, 'SUCCESS')
            time_taken = time.time() + self.start_time if hasattr(self, 'start_time') else 0
            hours, remainder = divmod(time_taken, 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f'{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}'
            messagebox.showinfo('处理完成', f'所有视频处理已完成！\n总处理时间: {time_str}')
        else:  # inserted
            messagebox.showerror('处理失败', f'视频处理失败：\n{message}')
        self.reset_ui_after_processing()
        self.root.after(1000, self._reset_completion_dialog_flag)

    def _reset_completion_dialog_flag(self):
        """重置完成弹窗标志位，允许下次处理时显示弹窗"""  # inserted
        VideoProcessingApp._completion_dialog_shown = False
        self.log_message('重置完成弹窗标志位', 'DEBUG')

    def update_volume_label(self, value):
        """更新BGM音量标签显示"""  # inserted
        percent = int(float(value) + 100)
        self.bgm_volume_label.config(text=f'{percent}%')
        if self.add_bgm_var.get() and self.bgm_path:
            basename = os.path.basename(self.bgm_path)
            if self.bgm_mode_var.get() == 1:
                self.param_bgm_label.config(text=f'BGM: {basename} (音量: {percent}%)')
            else:  # inserted
                self.param_bgm_label.config(text=f'BGM: {basename}文件夹 (音量: {percent}%)')

    def show_log_context_menu(self, event):
        """显示日志右键菜单"""  # inserted
        self.log_context_menu.post(event.x_root, event.y_root)

    def copy_log_selection(self):
        """复制选中的日志文本"""  # inserted
        try:
            selected_text = self.log_text.get(tk.SEL_FIRST, tk.SEL_LAST)
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
        except tk.TclError:
            return None

    def copy_all_logs(self):
        """复制所有日志"""  # inserted
        all_text = self.log_text.get(1.0, tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(all_text)

    def clear_logs(self):
        """清空日志"""  # inserted
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.insert(tk.END, '日志已清空\n')
        self.log_text.config(state=tk.DISABLED)

    def load_presets(self):
        """从磁盘读取预设文件"""  # inserted
        try:
            if os.path.exists(PRESET_FILE):
                with open(PRESET_FILE, 'r', encoding='utf-8') as f:
                    self.presets = json.load(f)
        except Exception as e:
                print(f'读取预设文件失败: {e}')
                self.presets = {}

    def save_presets(self):
        """将当前 self.presets 保存到磁盘"""  # inserted
        try:
            with open(PRESET_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.presets, f, ensure_ascii=False, indent=2)
        except Exception as e:
                print(f'保存预设文件失败: {e}')

    def refresh_preset_combo(self):
        """刷新下拉框显示"""  # inserted
        if hasattr(self, 'preset_combo'):
            self.preset_combo['values'] = list(self.presets.keys())

    def build_current_preset(self):
        """收集当前 UI 选项，返回预设字典"""  # inserted
        return {'add_bgm': self.add_bgm_var.get(), 'bgm_path': self.bgm_path, 'bgm_mode': self.bgm_mode_var.get(), 'bgm_volume': self.bgm_volume_var.get(), 'add_subtitles': self.add_subtitle_var.get(), 'subtitle_settings': self.subtitle_settings, 'create_merged_video': self.create_merged_video_var.get(), 'merge_original_video': self.merge_original_video_var.get(), 'parallel_processing': self.max_workers_var.get(), 'max_workers': self.custom_resolution_var.get(), 'custom_resolution': self.resolution_width_var.get(), 'resolution_width': self.resolution_height_var.get(), 'resolution_height': self.custom_bitrate_var.get(), 'custom_bitrate': self.bitrate_var.get()}

    def save_current_as_preset(self):
        """弹出对话框获取名称并保存当前设置为预设"""  # inserted
        name = simpledialog.askstring('保存预设', '请输入预设名称：', parent=self.root)
        if not name:
            return
        self.presets[name] = self.build_current_preset()
        self.save_presets()
        self.refresh_preset_combo()
        messagebox.showinfo('保存成功', f'已保存预设: {name}')

    def apply_selected_preset(self, event=None):
        """根据用户选择的预设填充界面各项"""  # inserted
        name = self.preset_combo.get()
        if name not in self.presets:
            return
        p = self.presets[name]
        self.add_bgm_var.set(p.get('add_bgm', False))
        self.bgm_path = p.get('bgm_path', '')
        self.bgm_path_label.config(text=os.path.basename(self.bgm_path) if self.bgm_path else '未选择BGM')
        self.bgm_mode_var.set(p.get('bgm_mode', 1))
        self.bgm_volume_var.set(p.get('bgm_volume', 0.2))
        self.update_volume_label(self.bgm_volume_var.get())
        self.add_subtitle_var.set(p.get('add_subtitles', False))
        self.subtitle_settings = p.get('subtitle_settings', None)
        if self.subtitle_settings:
            self.subtitle_style_label.config(text='字幕样式: 已设置')
        else:  # inserted
            self.subtitle_style_label.config(text='字幕样式: 未设置')
        self.create_merged_video_var.set(p.get('create_merged_video', False))
        self.merge_original_video_var.set(p.get('merge_original_video', False))
        self.parallel_processing_var.set(p.get('parallel_processing', True))
        self.max_workers_var.set(p.get('max_workers', min(4, os.cpu_count() or 4)))
        self.custom_resolution_var.set(p.get('custom_resolution', False))
        self.resolution_width_var.set(p.get('resolution_width', '1280'))
        self.resolution_height_var.set(p.get('resolution_height', '720'))
        self.custom_bitrate_var.set(p.get('custom_bitrate', False))
        self.bitrate_var.set(p.get('bitrate', '2000'))
        self.log_message(f'已应用预设: {name}', 'SUCCESS')

    def delete_selected_preset(self):
        """删除当前下拉框选中的预设"""  # inserted
        name = self.preset_combo.get()
        if not name:
            messagebox.showwarning('提示', '请选择需要删除的预设！')
            return
        if messagebox.askyesno('确认删除', f'确定删除预设: {name} ?'):
            if name in self.presets:
                del self.presets[name]
                self.save_presets()
                self.refresh_preset_combo()
                self.preset_combo.set('')
                messagebox.showinfo('删除成功', f'已删除预设: {name}')
            else:  # inserted
                messagebox.showwarning('提示', '预设不存在！')

    def on_close(self):
        """处理窗口关闭事件，安全终止后台线程和 FFmpeg 进程"""  # inserted
        try:
            if self.processing_thread and self.processing_thread.is_alive():
                if not messagebox.askyesno('正在处理', '当前仍有任务在运行，确定要强制退出并终止 FFmpeg 吗？'):
                    return
                try:
                    self.processing_thread.cancel()
                except Exception as e:
                    print(f'取消处理线程时出错: {e}')
                self.processing_thread.join(timeout=5)
            self._terminate_ffmpeg()
        finally:
            self.root.destroy()

    def _terminate_ffmpeg(self):
        """强制结束系统中所有 ffmpeg / ffprobe 进程（仅限本工具启动的）"""  # inserted
        try:
            if platform.system() == 'Windows':
                subprocess.run(['taskkill', '/F', '/T', '/IM', 'ffmpeg.exe'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                subprocess.run(['taskkill', '/F', '/T', '/IM', 'ffprobe.exe'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            else:  # inserted
                subprocess.run(['pkill', '-9', 'ffmpeg'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                subprocess.run(['pkill', '-9', 'ffprobe'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print('已尝试终止所有 FFmpeg 相关进程')
        except Exception as e:
            print(f'终止 FFmpeg 进程时出错: {e}')

class Logger:
    def __init__(self, logging_level=logging.INFO):
        self.level = logging_level
        logging.basicConfig(level=logging_level, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

    def info(self, message):
        logging.info(message)

    def warning(self, message):
        logging.warning(message)

    def error(self, message):
        logging.error(message)

    def debug(self, message):
        logging.debug(message)

def check_ffmpeg():
    """检查系统中是否安装了FFmpeg"""  # inserted
    try:
        if platform.system() == 'Windows':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags = subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            creation_flags = subprocess.CREATE_NO_WINDOW
            subprocess.run(['ffmpeg', '-version'], startupinfo=startupinfo, creationflags=creation_flags, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        else:  # inserted
            subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, check=True)
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        return False

def hide_console():
    """隐藏控制台窗口（仅在Windows系统下有效）"""  # inserted
    if sys.platform == 'win32':
        try:
            import ctypes
            hwnd = ctypes.windll.kernel32.GetConsoleWindow()
            if hwnd!= 0:
                ctypes.windll.user32.ShowWindow(hwnd, 0)
        except Exception as e:
            print(f'隐藏控制台窗口失败: {e}')

def get_hidden_startupinfo():
    """获取隐藏控制台窗口的启动信息（仅在Windows下有效）"""  # inserted
    if platform.system() == 'Windows':
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags = subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE
        creation_flags = subprocess.CREATE_NO_WINDOW | subprocess.DETACHED_PROCESS
        return (startupinfo, creation_flags)
    return (None, 0)
PRESET_FILE = 'parameter_presets.json'

def run():
    if not check_ffmpeg():
        print("警告: 未检测到 FFmpeg，程序功能可能受限")
    hide_console()
    root = tk.Tk()
    try:
        if platform.system() == 'Windows':
            root.iconbitmap(default='icon.ico')
    except:
        pass
    style = ttk.Style()
    if platform.system() == 'Windows':
        try:
            style.theme_use('vista')
        except:
            style.theme_use('clam')
    else:  # inserted
        try:
            style.theme_use('clam')
        except:
            pass
    app = VideoProcessingApp(root)
    root.mainloop()
if __name__ == '__main__':
    try:
        run()
    except Exception as e:
        print(f"程序启动错误: {e}")
        import traceback
        traceback.print_exc()